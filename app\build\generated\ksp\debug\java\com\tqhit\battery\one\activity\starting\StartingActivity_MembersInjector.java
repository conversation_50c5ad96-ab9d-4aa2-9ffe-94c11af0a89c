package com.tqhit.battery.one.activity.starting;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StartingActivity_MembersInjector implements MembersInjector<StartingActivity> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  public StartingActivity_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
  }

  public static MembersInjector<StartingActivity> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider) {
    return new StartingActivity_MembersInjector(applovinInterstitialAdManagerProvider, applovinNativeAdManagerProvider);
  }

  @Override
  public void injectMembers(StartingActivity instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.starting.StartingActivity.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(StartingActivity instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.starting.StartingActivity.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(StartingActivity instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }
}
