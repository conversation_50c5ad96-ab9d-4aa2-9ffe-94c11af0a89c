package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService;
import com.tqhit.battery.one.service.thumbnail.ThumbnailDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory implements Factory<DeferredThumbnailPreloadingService> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<ThumbnailDataService> thumbnailDataServiceProvider;

  private final Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider;

  private final Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider;

  public ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory(
      Provider<Context> contextProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<ThumbnailDataService> thumbnailDataServiceProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider) {
    this.contextProvider = contextProvider;
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.thumbnailDataServiceProvider = thumbnailDataServiceProvider;
    this.thumbnailPreloadingRepositoryProvider = thumbnailPreloadingRepositoryProvider;
    this.firebaseInitMonitorProvider = firebaseInitMonitorProvider;
  }

  @Override
  public DeferredThumbnailPreloadingService get() {
    return provideDeferredThumbnailPreloadingService(contextProvider.get(), animationDataServiceProvider.get(), thumbnailDataServiceProvider.get(), thumbnailPreloadingRepositoryProvider.get(), firebaseInitMonitorProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory create(
      Provider<Context> contextProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<ThumbnailDataService> thumbnailDataServiceProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider) {
    return new ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory(contextProvider, animationDataServiceProvider, thumbnailDataServiceProvider, thumbnailPreloadingRepositoryProvider, firebaseInitMonitorProvider);
  }

  public static DeferredThumbnailPreloadingService provideDeferredThumbnailPreloadingService(
      Context context, AnimationDataService animationDataService,
      ThumbnailDataService thumbnailDataService,
      ThumbnailPreloadingRepository thumbnailPreloadingRepository,
      FirebaseInitializationMonitor firebaseInitMonitor) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideDeferredThumbnailPreloadingService(context, animationDataService, thumbnailDataService, thumbnailPreloadingRepository, firebaseInitMonitor));
  }
}
