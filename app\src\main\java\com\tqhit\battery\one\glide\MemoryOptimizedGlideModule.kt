package com.tqhit.battery.one.glide

import android.content.Context
import android.util.Log
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.engine.cache.LruResourceCache
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.RequestOptions
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Memory-optimized Glide configuration for TJ_BatteryOne app.
 * Implements efficient caching strategies with strict memory limits for thumbnail preloading.
 * 
 * Performance Targets:
 * - Memory cache: <10MB for thumbnails
 * - Disk cache: <20MB for thumbnails
 * - Bitmap format: RGB_565 for memory efficiency
 * - Cache strategy: Optimized for preloaded content
 */
@GlideModule
class MemoryOptimizedGlideModule : AppGlideModule() {
    
    companion object {
        private const val TAG = "MemoryOptimizedGlide"
        
        // Memory cache limits (in bytes)
        private const val MEMORY_CACHE_SIZE_MB = 10L
        private const val MEMORY_CACHE_SIZE_BYTES = MEMORY_CACHE_SIZE_MB * 1024 * 1024
        
        // Disk cache limits (in bytes)
        private const val DISK_CACHE_SIZE_MB = 20L
        private const val DISK_CACHE_SIZE_BYTES = DISK_CACHE_SIZE_MB * 1024 * 1024
        
        // Cache directory name
        private const val DISK_CACHE_DIRECTORY = "glide_thumbnail_cache"
    }
    
    override fun applyOptions(context: Context, builder: GlideBuilder) {
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Applying memory-optimized Glide configuration")
        
        // Configure memory cache with strict size limit
        configureMemoryCache(context, builder)
        
        // Configure disk cache with size limit
        configureDiskCache(context, builder)
        
        // Set default request options for memory efficiency
        configureDefaultOptions(builder)
        
        // Configure logging for debugging
        configureLogging(builder)
        
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Memory-optimized configuration applied successfully")
    }
    
    /**
     * Configures memory cache with optimized size calculation.
     */
    private fun configureMemoryCache(context: Context, builder: GlideBuilder) {
        val calculator = MemorySizeCalculator.Builder(context)
            .setMemoryCacheScreens(2.0f) // Cache for 2 screens worth of images
            .setBitmapPoolScreens(3.0f)  // Pool for 3 screens worth of bitmaps
            .setMaxSizeMultiplier(0.4f)  // Use 40% of available memory
            .setLowMemoryMaxSizeMultiplier(0.2f) // Use 20% during low memory
            .build()
        
        // Use the smaller of calculated size or our target size
        val calculatedSize = calculator.memoryCacheSize
        val targetSize = MEMORY_CACHE_SIZE_BYTES.toInt()
        val finalSize = minOf(calculatedSize, targetSize)
        
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Memory cache - Calculated: ${calculatedSize / (1024 * 1024)}MB, Target: ${targetSize / (1024 * 1024)}MB, Final: ${finalSize / (1024 * 1024)}MB")
        
        builder.setMemoryCache(LruResourceCache(finalSize.toLong()))
        
        // Configure bitmap pool with similar logic
        val bitmapPoolSize = minOf(calculator.bitmapPoolSize, targetSize)
        builder.setBitmapPool(com.bumptech.glide.load.engine.bitmap_recycle.LruBitmapPool(bitmapPoolSize.toLong()))
        
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Bitmap pool size: ${bitmapPoolSize / (1024 * 1024)}MB")
    }
    
    /**
     * Configures disk cache with size limit and custom directory.
     */
    private fun configureDiskCache(context: Context, builder: GlideBuilder) {
        builder.setDiskCache(
            InternalCacheDiskCacheFactory(
                context,
                DISK_CACHE_DIRECTORY,
                DISK_CACHE_SIZE_BYTES
            )
        )
        
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Disk cache configured - Size: ${DISK_CACHE_SIZE_MB}MB, Directory: $DISK_CACHE_DIRECTORY")
    }
    
    /**
     * Configures default request options for memory efficiency.
     */
    private fun configureDefaultOptions(builder: GlideBuilder) {
        val defaultOptions = RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565) // Use RGB_565 for memory efficiency
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE) // Cache processed images
            .skipMemoryCache(false) // Enable memory caching
            .encodeFormat(android.graphics.Bitmap.CompressFormat.JPEG) // Use JPEG for smaller file sizes
            .encodeQuality(85) // Good quality with reasonable compression
        
        builder.setDefaultRequestOptions(defaultOptions)
        
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Default options configured - Format: RGB_565, Cache: RESOURCE, Quality: 85%")
    }
    
    /**
     * Configures logging for debugging and monitoring.
     */
    private fun configureLogging(builder: GlideBuilder) {
        if (com.tqhit.battery.one.BuildConfig.DEBUG) {
            builder.setLogLevel(Log.DEBUG)
            BatteryLogger.d(TAG, "GLIDE_CONFIG: Debug logging enabled")
        } else {
            builder.setLogLevel(Log.ERROR)
            BatteryLogger.d(TAG, "GLIDE_CONFIG: Production logging configured")
        }
    }
    
    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        // Register custom components if needed
        BatteryLogger.d(TAG, "GLIDE_CONFIG: Registering custom components")
        
        // Add custom model loaders or decoders here if needed
        // For now, using default components with optimized configuration
    }
    
    override fun isManifestParsingEnabled(): Boolean {
        // Disable manifest parsing for better performance
        return false
    }
}

/**
 * Memory-aware Glide request options for different use cases.
 */
object MemoryOptimizedGlideOptions {
    
    /**
     * Options for thumbnail loading with maximum memory efficiency.
     */
    fun thumbnailOptions(): RequestOptions {
        return RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .override(200, 200) // Limit thumbnail size
            .centerCrop()
            .encodeFormat(android.graphics.Bitmap.CompressFormat.JPEG)
            .encodeQuality(80)
    }
    
    /**
     * Options for preloaded thumbnail loading.
     */
    fun preloadedThumbnailOptions(): RequestOptions {
        return RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565)
            .diskCacheStrategy(DiskCacheStrategy.NONE) // Don't cache preloaded files
            .override(200, 200)
            .centerCrop()
            .skipMemoryCache(false) // Use memory cache for frequently accessed preloaded thumbnails
    }
    
    /**
     * Options for low memory conditions.
     */
    fun lowMemoryOptions(): RequestOptions {
        return RequestOptions()
            .format(DecodeFormat.PREFER_RGB_565)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .override(150, 150) // Smaller size for low memory
            .centerCrop()
            .encodeQuality(70) // Lower quality for memory savings
            .skipMemoryCache(true) // Skip memory cache during low memory
    }
    
    /**
     * Options for high-quality thumbnails when memory allows.
     */
    fun highQualityOptions(): RequestOptions {
        return RequestOptions()
            .format(DecodeFormat.PREFER_ARGB_8888)
            .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
            .override(300, 300)
            .centerCrop()
            .encodeFormat(android.graphics.Bitmap.CompressFormat.PNG)
            .encodeQuality(95)
    }
}

/**
 * Glide memory management utilities.
 */
object GlideMemoryManager {
    
    private const val TAG = "GlideMemoryManager"
    
    /**
     * Clears Glide memory cache.
     */
    fun clearMemoryCache(context: Context) {
        try {
            Glide.get(context).clearMemory()
            BatteryLogger.d(TAG, "GLIDE_MEMORY: Memory cache cleared successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "GLIDE_MEMORY: Error clearing memory cache", e)
        }
    }
    
    /**
     * Clears Glide disk cache (must be called on background thread).
     */
    suspend fun clearDiskCache(context: Context) {
        try {
            Glide.get(context).clearDiskCache()
            BatteryLogger.d(TAG, "GLIDE_MEMORY: Disk cache cleared successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "GLIDE_MEMORY: Error clearing disk cache", e)
        }
    }
    
    /**
     * Trims memory based on memory level.
     */
    fun trimMemory(context: Context, level: Int) {
        try {
            Glide.get(context).onTrimMemory(level)
            BatteryLogger.d(TAG, "GLIDE_MEMORY: Memory trimmed for level: $level")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "GLIDE_MEMORY: Error trimming memory", e)
        }
    }
    
    /**
     * Gets current memory cache size.
     */
    fun getMemoryCacheSize(context: Context): Long {
        return try {
            val glide = Glide.get(context)
            // Note: Glide doesn't provide direct access to current cache size
            // This would need to be implemented with custom cache implementation
            0L
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "GLIDE_MEMORY: Error getting memory cache size", e)
            0L
        }
    }
}
