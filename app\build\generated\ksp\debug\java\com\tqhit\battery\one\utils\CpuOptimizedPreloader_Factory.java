package com.tqhit.battery.one.utils;

import android.content.Context;
import com.tqhit.battery.one.manager.quota.StorageQuotaManager;
import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import com.tqhit.battery.one.service.thumbnail.MemoryAwareThumbnailPreloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CpuOptimizedPreloader_Factory implements Factory<CpuOptimizedPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider;

  private final Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

  private final Provider<StorageQuotaManager> storageQuotaManagerProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  public CpuOptimizedPreloader_Factory(Provider<Context> contextProvider,
      Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<StorageQuotaManager> storageQuotaManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    this.contextProvider = contextProvider;
    this.memoryAwareThumbnailPreloaderProvider = memoryAwareThumbnailPreloaderProvider;
    this.animationPreloadingRepositoryProvider = animationPreloadingRepositoryProvider;
    this.storageQuotaManagerProvider = storageQuotaManagerProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
  }

  @Override
  public CpuOptimizedPreloader get() {
    return newInstance(contextProvider.get(), memoryAwareThumbnailPreloaderProvider.get(), animationPreloadingRepositoryProvider.get(), storageQuotaManagerProvider.get(), memoryAnalyzerProvider.get());
  }

  public static CpuOptimizedPreloader_Factory create(Provider<Context> contextProvider,
      Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<StorageQuotaManager> storageQuotaManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    return new CpuOptimizedPreloader_Factory(contextProvider, memoryAwareThumbnailPreloaderProvider, animationPreloadingRepositoryProvider, storageQuotaManagerProvider, memoryAnalyzerProvider);
  }

  public static CpuOptimizedPreloader newInstance(Context context,
      MemoryAwareThumbnailPreloader memoryAwareThumbnailPreloader,
      AnimationPreloadingRepository animationPreloadingRepository,
      StorageQuotaManager storageQuotaManager, MemoryAnalyzer memoryAnalyzer) {
    return new CpuOptimizedPreloader(context, memoryAwareThumbnailPreloader, animationPreloadingRepository, storageQuotaManager, memoryAnalyzer);
  }
}
