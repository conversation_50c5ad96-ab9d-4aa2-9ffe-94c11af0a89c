package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import com.tqhit.battery.one.utils.PerformanceProfiler;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvidePerformanceProfilerFactory implements Factory<PerformanceProfiler> {
  private final Provider<Context> contextProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  private final Provider<OptimizedStorageManager> storageManagerProvider;

  public ThumbnailPreloadingModule_ProvidePerformanceProfilerFactory(
      Provider<Context> contextProvider, Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<OptimizedStorageManager> storageManagerProvider) {
    this.contextProvider = contextProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
    this.storageManagerProvider = storageManagerProvider;
  }

  @Override
  public PerformanceProfiler get() {
    return providePerformanceProfiler(contextProvider.get(), memoryAnalyzerProvider.get(), storageManagerProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvidePerformanceProfilerFactory create(
      Provider<Context> contextProvider, Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<OptimizedStorageManager> storageManagerProvider) {
    return new ThumbnailPreloadingModule_ProvidePerformanceProfilerFactory(contextProvider, memoryAnalyzerProvider, storageManagerProvider);
  }

  public static PerformanceProfiler providePerformanceProfiler(Context context,
      MemoryAnalyzer memoryAnalyzer, OptimizedStorageManager storageManager) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.providePerformanceProfiler(context, memoryAnalyzer, storageManager));
  }
}
