<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="16dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/tv_dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/using_energy"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/black" />

        <ImageView
            android:id="@+id/iv_close_dialog"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/close" />

    </LinearLayout>

    <!-- Session Summary Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/grey_block"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/current_session_summary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/discharge_session_duration"
                android:textSize="12sp"
                android:textColor="?attr/black" />

            <TextView
                android:id="@+id/tv_session_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="?attr/black" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/estimated_total_consumption"
                android:textSize="12sp"
                android:textColor="?attr/black" />

            <TextView
                android:id="@+id/tv_total_consumption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="?attr/black" />

        </LinearLayout>

    </LinearLayout>

    <!-- Loading/Error States -->
    <ProgressBar
        android:id="@+id/progress_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="32dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_error_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_power_consumption_error"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:gravity="center"
        android:padding="16dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_no_data_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/no_app_usage_data"
        android:textSize="14sp"
        android:textColor="?attr/black"
        android:gravity="center"
        android:padding="16dp"
        android:visibility="gone" />

    <!-- Apps List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_apps_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        android:scrollbars="vertical"
        android:visibility="gone" />

    <!-- Permission Request Section -->
    <LinearLayout
        android:id="@+id/ll_permission_request"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/usage_access_required"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/usage_access_explanation"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_permission_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/permission_status_checking"
            android:textSize="12sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_grant_permission"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/grant_permission"
            android:textColor="@color/white"
            android:background="@drawable/button_primary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_skip_permission"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/skip_for_now"
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:padding="8dp"
            android:background="?attr/selectableItemBackground" />

    </LinearLayout>

</LinearLayout>
