package com.tqhit.battery.one.di;

import android.content.Context;
import com.google.gson.Gson;
import com.tqhit.battery.one.service.firebase.LocalConfigFallbackService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideLocalConfigFallbackServiceFactory implements Factory<LocalConfigFallbackService> {
  private final Provider<Context> contextProvider;

  private final Provider<Gson> gsonProvider;

  public ThumbnailPreloadingModule_ProvideLocalConfigFallbackServiceFactory(
      Provider<Context> contextProvider, Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public LocalConfigFallbackService get() {
    return provideLocalConfigFallbackService(contextProvider.get(), gsonProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideLocalConfigFallbackServiceFactory create(
      Provider<Context> contextProvider, Provider<Gson> gsonProvider) {
    return new ThumbnailPreloadingModule_ProvideLocalConfigFallbackServiceFactory(contextProvider, gsonProvider);
  }

  public static LocalConfigFallbackService provideLocalConfigFallbackService(Context context,
      Gson gson) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideLocalConfigFallbackService(context, gson));
  }
}
