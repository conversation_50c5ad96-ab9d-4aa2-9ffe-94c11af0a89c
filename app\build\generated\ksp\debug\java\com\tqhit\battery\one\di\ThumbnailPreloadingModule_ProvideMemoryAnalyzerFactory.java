package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideMemoryAnalyzerFactory implements Factory<MemoryAnalyzer> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationFileManager> animationFileManagerProvider;

  private final Provider<ThumbnailFileManager> thumbnailFileManagerProvider;

  private final Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

  private final Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider;

  public ThumbnailPreloadingModule_ProvideMemoryAnalyzerFactory(Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.animationFileManagerProvider = animationFileManagerProvider;
    this.thumbnailFileManagerProvider = thumbnailFileManagerProvider;
    this.animationPreloadingRepositoryProvider = animationPreloadingRepositoryProvider;
    this.thumbnailPreloadingRepositoryProvider = thumbnailPreloadingRepositoryProvider;
  }

  @Override
  public MemoryAnalyzer get() {
    return provideMemoryAnalyzer(contextProvider.get(), animationFileManagerProvider.get(), thumbnailFileManagerProvider.get(), animationPreloadingRepositoryProvider.get(), thumbnailPreloadingRepositoryProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideMemoryAnalyzerFactory create(
      Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider) {
    return new ThumbnailPreloadingModule_ProvideMemoryAnalyzerFactory(contextProvider, animationFileManagerProvider, thumbnailFileManagerProvider, animationPreloadingRepositoryProvider, thumbnailPreloadingRepositoryProvider);
  }

  public static MemoryAnalyzer provideMemoryAnalyzer(Context context,
      AnimationFileManager animationFileManager, ThumbnailFileManager thumbnailFileManager,
      AnimationPreloadingRepository animationPreloadingRepository,
      ThumbnailPreloadingRepository thumbnailPreloadingRepository) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideMemoryAnalyzer(context, animationFileManager, thumbnailFileManager, animationPreloadingRepository, thumbnailPreloadingRepository));
  }
}
