package com.tqhit.battery.one.service.thumbnail

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.withContext
import java.io.File
import java.net.HttpURLConnection
import java.net.URL
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for preloading thumbnail images.
 * Handles concurrent downloads, caching, and error management for thumbnails.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles thumbnail preloading operations
 * - Open/Closed: Extensible for different download strategies
 * - Dependency Inversion: Depends on abstractions (ThumbnailFileManager)
 */
@Singleton
class ThumbnailPreloader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val fileManager: ThumbnailFileManager
) {
    companion object {
        private const val TAG = "ThumbnailPreloader"
        private const val MAX_CONCURRENT_DOWNLOADS = 4
        private const val DOWNLOAD_TIMEOUT_MS = 15_000L
        private const val BUFFER_SIZE = 8192
    }
    
    // Semaphore to limit concurrent downloads
    private val downloadSemaphore = Semaphore(MAX_CONCURRENT_DOWNLOADS)
    
    /**
     * Preloads a list of thumbnail items concurrently.
     * Returns a list of ThumbnailPreloadResult indicating success/failure for each item.
     */
    suspend fun preloadThumbnails(thumbnails: List<ThumbnailItem>): List<ThumbnailPreloadResult> = withContext(Dispatchers.IO) {
        if (thumbnails.isEmpty()) {
            BatteryLogger.w(TAG, "No thumbnails provided for preloading")
            return@withContext emptyList()
        }
        
        BatteryLogger.d(TAG, "Starting preload of ${thumbnails.size} thumbnails")
        
        try {
            // Use coroutineScope to ensure all downloads complete or fail together
            coroutineScope {
                thumbnails.map { thumbnail ->
                    async {
                        preloadSingleThumbnail(thumbnail)
                    }
                }.awaitAll()
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during batch thumbnail preloading", e)
            // Return failure results for all thumbnails
            thumbnails.map { thumbnail ->
                ThumbnailPreloadResult.Failure(
                    thumbnailUrl = thumbnail.thumbnailUrl,
                    errorMessage = "Batch preloading failed: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * Preloads a single thumbnail with proper error handling and duplicate checking.
     */
    private suspend fun preloadSingleThumbnail(thumbnail: ThumbnailItem): ThumbnailPreloadResult = withContext(Dispatchers.IO) {
        // Acquire semaphore to limit concurrent downloads
        downloadSemaphore.acquire()
        
        try {
            BatteryLogger.d(TAG, "Starting thumbnail preload for: ${thumbnail.thumbnailUrl}")
            
            // Check if file already exists
            val existingFile = fileManager.getPreloadedThumbnail(
                thumbnail.thumbnailUrl,
                thumbnail.categoryName,
                thumbnail.animationMediaUrl
            )
            if (existingFile != null && existingFile.status == ThumbnailPreloadStatus.COMPLETED) {
                BatteryLogger.d(TAG, "Thumbnail already preloaded: ${thumbnail.thumbnailUrl}")
                return@withContext ThumbnailPreloadResult.AlreadyExists(existingFile)
            }
            
            // Validate URL
            if (!isValidUrl(thumbnail.thumbnailUrl)) {
                val errorMsg = "Invalid thumbnail URL format: ${thumbnail.thumbnailUrl}"
                BatteryLogger.w(TAG, errorMsg)
                return@withContext ThumbnailPreloadResult.Failure(
                    thumbnailUrl = thumbnail.thumbnailUrl,
                    errorMessage = errorMsg
                )
            }
            
            // Create destination file
            val destFile = fileManager.createThumbnailFile(thumbnail.thumbnailUrl)
            
            // Download the thumbnail
            val downloadResult = downloadThumbnail(thumbnail.thumbnailUrl, destFile)
            
            if (downloadResult.isSuccess) {
                // Validate downloaded file
                if (fileManager.validateDownloadedThumbnail(destFile)) {
                    val preloadedItem = PreloadedThumbnailItem(
                        thumbnailUrl = thumbnail.thumbnailUrl,
                        localFilePath = destFile.absolutePath,
                        status = ThumbnailPreloadStatus.COMPLETED,
                        downloadTimestamp = System.currentTimeMillis(),
                        fileSizeBytes = destFile.length(),
                        categoryName = thumbnail.categoryName,
                        animationMediaUrl = thumbnail.animationMediaUrl
                    )
                    
                    BatteryLogger.d(TAG, "Successfully preloaded thumbnail: ${thumbnail.thumbnailUrl} (${destFile.length()} bytes)")
                    ThumbnailPreloadResult.Success(preloadedItem)
                } else {
                    // Clean up invalid file
                    destFile.delete()
                    val errorMsg = "Downloaded thumbnail failed validation"
                    BatteryLogger.w(TAG, "$errorMsg: ${thumbnail.thumbnailUrl}")
                    ThumbnailPreloadResult.Failure(
                        thumbnailUrl = thumbnail.thumbnailUrl,
                        errorMessage = errorMsg
                    )
                }
            } else {
                val errorMsg = downloadResult.exceptionOrNull()?.message ?: "Unknown download error"
                BatteryLogger.w(TAG, "Failed to download thumbnail: ${thumbnail.thumbnailUrl} - $errorMsg")
                ThumbnailPreloadResult.Failure(
                    thumbnailUrl = thumbnail.thumbnailUrl,
                    errorMessage = errorMsg,
                    exception = downloadResult.exceptionOrNull()
                )
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error preloading thumbnail: ${thumbnail.thumbnailUrl}", e)
            ThumbnailPreloadResult.Failure(
                thumbnailUrl = thumbnail.thumbnailUrl,
                errorMessage = "Preload error: ${e.message}",
                exception = e
            )
        } finally {
            downloadSemaphore.release()
        }
    }
    
    /**
     * Downloads a thumbnail from URL to the specified file.
     * Returns Result indicating success or failure.
     */
    private suspend fun downloadThumbnail(thumbnailUrl: String, destFile: File): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val url = URL(thumbnailUrl)
            val connection = url.openConnection() as HttpURLConnection
            
            connection.apply {
                requestMethod = "GET"
                connectTimeout = DOWNLOAD_TIMEOUT_MS.toInt()
                readTimeout = DOWNLOAD_TIMEOUT_MS.toInt()
                setRequestProperty("User-Agent", "BatteryOne-ThumbnailPreloader/1.0")
            }
            
            val responseCode = connection.responseCode
            if (responseCode != HttpURLConnection.HTTP_OK) {
                return@withContext Result.failure(
                    Exception("HTTP error: $responseCode for $thumbnailUrl")
                )
            }
            
            connection.inputStream.use { input ->
                destFile.outputStream().use { output ->
                    val buffer = ByteArray(BUFFER_SIZE)
                    var bytesRead: Int
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                    }
                }
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            // Clean up partial file on error
            if (destFile.exists()) {
                destFile.delete()
            }
            Result.failure(e)
        }
    }
    
    /**
     * Validates if a URL is properly formatted and accessible.
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlObj = URL(url)
            urlObj.protocol in listOf("http", "https") && 
            urlObj.host.isNotBlank()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Checks if a thumbnail is already preloaded and valid.
     */
    suspend fun isThumbnailPreloaded(thumbnailUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val preloadedItem = fileManager.getPreloadedThumbnail(thumbnailUrl)
            preloadedItem != null && 
            preloadedItem.status == ThumbnailPreloadStatus.COMPLETED &&
            File(preloadedItem.localFilePath).exists()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking thumbnail preload status for $thumbnailUrl", e)
            false
        }
    }
    
    /**
     * Gets a preloaded thumbnail file path if available.
     */
    suspend fun getPreloadedThumbnailPath(thumbnailUrl: String): String? = withContext(Dispatchers.IO) {
        try {
            val preloadedItem = fileManager.getPreloadedThumbnail(thumbnailUrl)
            if (preloadedItem?.status == ThumbnailPreloadStatus.COMPLETED) {
                val file = File(preloadedItem.localFilePath)
                if (file.exists()) {
                    return@withContext preloadedItem.localFilePath
                }
            }
            null
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloaded thumbnail path for $thumbnailUrl", e)
            null
        }
    }
}
