@echo off
REM TJ_BatteryOne Performance Validation Script
REM Validates all performance requirements and benchmarks
REM Usage: performance_validation.bat [device_id]

setlocal enabledelayedexpansion

REM Configuration
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set DEVICE_ID=%1
if "%DEVICE_ID%"=="" set DEVICE_ID=emulator-5554

REM Performance benchmarks (in milliseconds)
set BENCHMARK_COLD_START=3000
set BENCHMARK_FRAGMENT_SWITCH=500
set BENCHMARK_DATA_FLOW=100
set BENCHMARK_ANIMATION_LOAD=500
set BENCHMARK_THUMBNAIL_LOAD=100

REM Memory benchmarks (in KB)
set BENCHMARK_MEMORY_USAGE=102400
set BENCHMARK_VIDEO_CACHE=51200
set BENCHMARK_THUMBNAIL_CACHE=10240

echo ========================================
echo TJ_BatteryOne Performance Validation
echo ========================================
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo.
echo PERFORMANCE BENCHMARKS:
echo - Cold Start: ^<%BENCHMARK_COLD_START%ms
echo - Fragment Switch: ^<%BENCHMARK_FRAGMENT_SWITCH%ms
echo - Data Flow: ^<%BENCHMARK_DATA_FLOW%ms
echo - Animation Load: ^<%BENCHMARK_ANIMATION_LOAD%ms
echo - Thumbnail Load: ^<%BENCHMARK_THUMBNAIL_LOAD%ms
echo.
echo MEMORY BENCHMARKS:
echo - App Memory: ^<%BENCHMARK_MEMORY_USAGE% KB
echo - Video Cache: ^<%BENCHMARK_VIDEO_CACHE% KB
echo - Thumbnail Cache: ^<%BENCHMARK_THUMBNAIL_CACHE% KB
echo ========================================

REM Check ADB connection
echo [INFO] Checking ADB connection...
%ADB_PATH% -s %DEVICE_ID% shell echo "Device connected" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot connect to device %DEVICE_ID%
    exit /b 1
)
echo [SUCCESS] Device connected

REM Create validation results directory
set VALIDATION_DIR=validation_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set VALIDATION_DIR=%VALIDATION_DIR: =0%
mkdir "%VALIDATION_DIR%" 2>nul

echo [INFO] Validation results: %VALIDATION_DIR%

REM Initialize test results
set TESTS_PASSED=0
set TESTS_FAILED=0
set TOTAL_TESTS=8

REM Start logcat monitoring
echo [INFO] Starting logcat monitoring...
start /b %ADB_PATH% -s %DEVICE_ID% logcat -s STARTUP_TIMING:D FRAGMENT_SWITCH:D DATA_FLOW_LATENCY:D PERFORMANCE_BENCHMARK:D MEMORY_USAGE:D > "%VALIDATION_DIR%\validation_logcat.log"

REM Test 1: Cold Start Performance
echo.
echo [TEST 1/8] Cold Start Performance
echo =================================
echo [INFO] Testing cold start performance...

%ADB_PATH% -s %DEVICE_ID% shell am force-stop %APP_ID%
timeout /t 3 >nul

%ADB_PATH% -s %DEVICE_ID% shell am start -W -n %APP_ID%/com.tqhit.battery.one.activity.starting.StartingActivity > "%VALIDATION_DIR%\cold_start.txt"

for /f "tokens=2 delims=:" %%a in ('findstr "TotalTime" "%VALIDATION_DIR%\cold_start.txt"') do (
    set COLD_START_TIME=%%a
    set COLD_START_TIME=!COLD_START_TIME: =!
)

echo [RESULT] Cold Start Time: %COLD_START_TIME%ms
if %COLD_START_TIME% LEQ %BENCHMARK_COLD_START% (
    echo [PASS] Cold start within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Cold start exceeds benchmark
    set /a TESTS_FAILED+=1
)

timeout /t 5 >nul

REM Test 2: Memory Usage Baseline
echo.
echo [TEST 2/8] Memory Usage Validation
echo ==================================
echo [INFO] Validating memory usage...

%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% > "%VALIDATION_DIR%\memory_usage.txt"

for /f "tokens=2" %%a in ('findstr "TOTAL" "%VALIDATION_DIR%\memory_usage.txt"') do (
    set MEMORY_USAGE=%%a
    goto :memory_found
)
:memory_found

echo [RESULT] Memory Usage: %MEMORY_USAGE% KB
if %MEMORY_USAGE% LEQ %BENCHMARK_MEMORY_USAGE% (
    echo [PASS] Memory usage within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Memory usage exceeds benchmark
    set /a TESTS_FAILED+=1
)

REM Test 3: Fragment Switch Performance
echo.
echo [TEST 3/8] Fragment Switch Performance
echo =====================================
echo [INFO] Testing fragment switching...

REM Clear logcat and perform fragment switches
%ADB_PATH% -s %DEVICE_ID% logcat -c

REM Navigate to trigger fragment switches
%ADB_PATH% -s %DEVICE_ID% shell input tap 300 1500
timeout /t 1 >nul
%ADB_PATH% -s %DEVICE_ID% shell input tap 500 1500
timeout /t 1 >nul
%ADB_PATH% -s %DEVICE_ID% shell input tap 700 1500
timeout /t 2 >nul

REM Check for fragment switch timing in logcat
%ADB_PATH% -s %DEVICE_ID% logcat -d -s FRAGMENT_SWITCH:D | findstr "FRAGMENT_SWITCH_TIMING" > "%VALIDATION_DIR%\fragment_switch.txt"

set FRAGMENT_SWITCH_PASS=1
for /f "tokens=4 delims== " %%a in ('type "%VALIDATION_DIR%\fragment_switch.txt"') do (
    set SWITCH_TIME=%%a
    set SWITCH_TIME=!SWITCH_TIME:ms=!
    if !SWITCH_TIME! GTR %BENCHMARK_FRAGMENT_SWITCH% set FRAGMENT_SWITCH_PASS=0
)

if %FRAGMENT_SWITCH_PASS%==1 (
    echo [PASS] Fragment switching within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Fragment switching exceeds benchmark
    set /a TESTS_FAILED+=1
)

REM Test 4: Data Flow Latency
echo.
echo [TEST 4/8] Data Flow Latency
echo ============================
echo [INFO] Testing data flow performance...

%ADB_PATH% -s %DEVICE_ID% logcat -c

REM Trigger data flow operations
%ADB_PATH% -s %DEVICE_ID% shell input tap 400 1200
timeout /t 2 >nul

%ADB_PATH% -s %DEVICE_ID% logcat -d -s DATA_FLOW_LATENCY:D | findstr "DATA_FLOW_TIMING" > "%VALIDATION_DIR%\data_flow.txt"

set DATA_FLOW_PASS=1
for /f "tokens=4 delims== " %%a in ('type "%VALIDATION_DIR%\data_flow.txt"') do (
    set FLOW_TIME=%%a
    set FLOW_TIME=!FLOW_TIME:ms=!
    if !FLOW_TIME! GTR %BENCHMARK_DATA_FLOW% set DATA_FLOW_PASS=0
)

if %DATA_FLOW_PASS%==1 (
    echo [PASS] Data flow within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Data flow exceeds benchmark
    set /a TESTS_FAILED+=1
)

REM Test 5: Animation Load Performance
echo.
echo [TEST 5/8] Animation Load Performance
echo ====================================
echo [INFO] Testing animation loading...

%ADB_PATH% -s %DEVICE_ID% logcat -c

REM Navigate to animation section and trigger loading
%ADB_PATH% -s %DEVICE_ID% shell input tap 500 1500
timeout /t 3 >nul

%ADB_PATH% -s %DEVICE_ID% logcat -d -s PERFORMANCE_BENCHMARK:D | findstr "animation_load" > "%VALIDATION_DIR%\animation_load.txt"

set ANIMATION_LOAD_PASS=1
for /f "tokens=6 delims== " %%a in ('type "%VALIDATION_DIR%\animation_load.txt"') do (
    set LOAD_TIME=%%a
    set LOAD_TIME=!LOAD_TIME:ms=!
    if !LOAD_TIME! GTR %BENCHMARK_ANIMATION_LOAD% set ANIMATION_LOAD_PASS=0
)

if %ANIMATION_LOAD_PASS%==1 (
    echo [PASS] Animation loading within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Animation loading exceeds benchmark
    set /a TESTS_FAILED+=1
)

REM Test 6: Thumbnail Load Performance
echo.
echo [TEST 6/8] Thumbnail Load Performance
echo ====================================
echo [INFO] Testing thumbnail loading...

%ADB_PATH% -s %DEVICE_ID% logcat -c
timeout /t 2 >nul

%ADB_PATH% -s %DEVICE_ID% logcat -d -s PERFORMANCE_BENCHMARK:D | findstr "thumbnail_load" > "%VALIDATION_DIR%\thumbnail_load.txt"

set THUMBNAIL_LOAD_PASS=1
for /f "tokens=6 delims== " %%a in ('type "%VALIDATION_DIR%\thumbnail_load.txt"') do (
    set THUMB_TIME=%%a
    set THUMB_TIME=!THUMB_TIME:ms=!
    if !THUMB_TIME! GTR %BENCHMARK_THUMBNAIL_LOAD% set THUMBNAIL_LOAD_PASS=0
)

if %THUMBNAIL_LOAD_PASS%==1 (
    echo [PASS] Thumbnail loading within benchmark
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Thumbnail loading exceeds benchmark
    set /a TESTS_FAILED+=1
)

REM Test 7: Cache Size Validation
echo.
echo [TEST 7/8] Cache Size Validation
echo ===============================
echo [INFO] Validating cache sizes...

REM Get cache directory sizes
%ADB_PATH% -s %DEVICE_ID% shell du -s /data/data/%APP_ID%/files/preloaded_animations 2>nul | findstr -v "Permission" > "%VALIDATION_DIR%\video_cache_size.txt"
%ADB_PATH% -s %DEVICE_ID% shell du -s /data/data/%APP_ID%/files/preloaded_thumbnails 2>nul | findstr -v "Permission" > "%VALIDATION_DIR%\thumbnail_cache_size.txt"

set VIDEO_CACHE_SIZE=0
set THUMBNAIL_CACHE_SIZE=0

for /f "tokens=1" %%a in ('type "%VALIDATION_DIR%\video_cache_size.txt" 2^>nul') do set VIDEO_CACHE_SIZE=%%a
for /f "tokens=1" %%a in ('type "%VALIDATION_DIR%\thumbnail_cache_size.txt" 2^>nul') do set THUMBNAIL_CACHE_SIZE=%%a

echo [RESULT] Video Cache: %VIDEO_CACHE_SIZE% KB
echo [RESULT] Thumbnail Cache: %THUMBNAIL_CACHE_SIZE% KB

set CACHE_SIZE_PASS=1
if %VIDEO_CACHE_SIZE% GTR %BENCHMARK_VIDEO_CACHE% set CACHE_SIZE_PASS=0
if %THUMBNAIL_CACHE_SIZE% GTR %BENCHMARK_THUMBNAIL_CACHE% set CACHE_SIZE_PASS=0

if %CACHE_SIZE_PASS%==1 (
    echo [PASS] Cache sizes within benchmarks
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] Cache sizes exceed benchmarks
    set /a TESTS_FAILED+=1
)

REM Test 8: Performance Violations Check
echo.
echo [TEST 8/8] Performance Violations Check
echo ======================================
echo [INFO] Checking for performance violations...

%ADB_PATH% -s %DEVICE_ID% logcat -d -s PERFORMANCE_BENCHMARK:W | findstr "VIOLATION" > "%VALIDATION_DIR%\violations.txt"

for /f %%a in ('type "%VALIDATION_DIR%\violations.txt" 2^>nul ^| find /c "VIOLATION"') do set VIOLATION_COUNT=%%a

echo [RESULT] Performance Violations: %VIOLATION_COUNT%

if %VIOLATION_COUNT%==0 (
    echo [PASS] No performance violations detected
    set /a TESTS_PASSED+=1
) else (
    echo [FAIL] %VIOLATION_COUNT% performance violations detected
    set /a TESTS_FAILED+=1
)

REM Stop logcat monitoring
taskkill /f /im adb.exe >nul 2>&1

REM Generate validation report
echo.
echo ========================================
echo Validation Complete
echo ========================================

set /a PASS_RATE=%TESTS_PASSED% * 100 / %TOTAL_TESTS%

(
echo TJ_BatteryOne Performance Validation Report
echo Generated: %date% %time%
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo.
echo VALIDATION RESULTS:
echo - Tests Passed: %TESTS_PASSED%/%TOTAL_TESTS%
echo - Tests Failed: %TESTS_FAILED%/%TOTAL_TESTS%
echo - Pass Rate: %PASS_RATE%%%
echo.
echo DETAILED RESULTS:
echo 1. Cold Start: %COLD_START_TIME%ms ^(Benchmark: ^<%BENCHMARK_COLD_START%ms^)
echo 2. Memory Usage: %MEMORY_USAGE% KB ^(Benchmark: ^<%BENCHMARK_MEMORY_USAGE% KB^)
echo 3. Fragment Switch: See fragment_switch.txt
echo 4. Data Flow: See data_flow.txt
echo 5. Animation Load: See animation_load.txt
echo 6. Thumbnail Load: See thumbnail_load.txt
echo 7. Video Cache: %VIDEO_CACHE_SIZE% KB ^(Benchmark: ^<%BENCHMARK_VIDEO_CACHE% KB^)
echo 8. Thumbnail Cache: %THUMBNAIL_CACHE_SIZE% KB ^(Benchmark: ^<%BENCHMARK_THUMBNAIL_CACHE% KB^)
echo 9. Violations: %VIOLATION_COUNT% detected
echo.
echo OVERALL STATUS:
if %PASS_RATE% GEQ 90 echo EXCELLENT - All critical benchmarks met
if %PASS_RATE% GEQ 75 if %PASS_RATE% LSS 90 echo GOOD - Most benchmarks met, minor optimizations needed
if %PASS_RATE% GEQ 50 if %PASS_RATE% LSS 75 echo NEEDS IMPROVEMENT - Several benchmarks failed
if %PASS_RATE% LSS 50 echo CRITICAL - Major performance issues detected
echo.
echo RECOMMENDATIONS:
if %TESTS_FAILED% GTR 0 echo - Review failed tests and implement optimizations
if %VIOLATION_COUNT% GTR 0 echo - Address %VIOLATION_COUNT% performance violations
echo - Monitor memory usage patterns for optimization opportunities
echo - Review logcat for detailed performance insights
) > "%VALIDATION_DIR%\validation_report.txt"

echo [INFO] Validation completed!
echo [INFO] Tests Passed: %TESTS_PASSED%/%TOTAL_TESTS% (%PASS_RATE%%%)
echo [INFO] Results saved to: %VALIDATION_DIR%
echo [INFO] Review validation_report.txt for detailed analysis

if %PASS_RATE% GEQ 90 (
    echo [SUCCESS] Excellent performance - all benchmarks met!
) else if %PASS_RATE% GEQ 75 (
    echo [WARNING] Good performance - minor optimizations recommended
) else (
    echo [ERROR] Performance issues detected - optimization required
)

REM Open results folder
start "" "%VALIDATION_DIR%"

endlocal
pause
