<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/btn_test_discharge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test New Discharge Fragment"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.4" />
        
    <Button
        android:id="@+id/btn_test_charge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test New Charge Fragment"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/btn_test_discharge"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 