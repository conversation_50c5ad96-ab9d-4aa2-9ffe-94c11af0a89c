package com.tqhit.battery.one.service.firebase;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class FirebaseInitializationMonitor_Factory implements Factory<FirebaseInitializationMonitor> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  public FirebaseInitializationMonitor_Factory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
  }

  @Override
  public FirebaseInitializationMonitor get() {
    return newInstance(remoteConfigHelperProvider.get());
  }

  public static FirebaseInitializationMonitor_Factory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    return new FirebaseInitializationMonitor_Factory(remoteConfigHelperProvider);
  }

  public static FirebaseInitializationMonitor newInstance(
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    return new FirebaseInitializationMonitor(remoteConfigHelper);
  }
}
