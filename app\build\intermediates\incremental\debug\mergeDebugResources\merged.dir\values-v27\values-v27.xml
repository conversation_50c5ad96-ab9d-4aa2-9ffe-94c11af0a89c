<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.Theme.SplashScreen" parent="Base.v27.Theme.SplashScreen"/>
    <style name="Base.Theme.SplashScreen.Light" parent="Base.v27.Theme.SplashScreen.Light"/>
    <style name="Base.v27.Theme.SplashScreen" parent="Base.v21.Theme.SplashScreen">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="Base.v27.Theme.SplashScreen.Light" parent="Base.v21.Theme.SplashScreen.Light">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="FullscreenAdActivity" parent="@style/Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="mbridge_common_activity_style" parent="@android:style/Theme.Translucent.NoTitleBar"/>
    <style name="mbridge_transparent_common_activity_style" parent="mbridge_common_activity_style">
    </style>
</resources>