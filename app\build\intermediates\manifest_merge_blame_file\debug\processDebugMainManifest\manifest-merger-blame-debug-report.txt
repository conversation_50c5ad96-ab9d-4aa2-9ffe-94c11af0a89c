1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
4    android:versionCode="43"
5    android:versionName="1.2.0.20250624" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
15    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
22
23    <permission
23-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-20:47
24        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
24-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:9-71
25        android:protectionLevel="signature" />
25-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:20:9-44
26
27    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba030faa7d4b5da9633290db8b6e3cf1\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
29    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
29-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:5-79
29-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:10:22-76
30
31    <queries>
31-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:8:5-12:15
32        <intent>
32-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:9:9-11:18
33            <action android:name="com.attribution.REFERRAL_PROVIDER" />
33-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:10:13-72
33-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:10:21-69
34        </intent>
35        <intent>
35-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:14:9-16:18
36            <action android:name="androidx.browser.customtabs.CustomTabsService" />
36-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:13-84
36-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:15:21-81
37        </intent>
38        <intent>
38-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:17:9-23:18
39            <action android:name="android.intent.action.VIEW" />
39-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
39-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
40
41            <category android:name="android.intent.category.BROWSABLE" />
41-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
41-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
42
43            <data android:scheme="https" />
43-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
43-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
44        </intent>
45        <intent>
45-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:24:9-30:18
46            <action android:name="android.intent.action.VIEW" />
46-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
46-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
47
48            <category android:name="android.intent.category.BROWSABLE" />
48-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
48-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
49
50            <data android:scheme="http" />
50-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
50-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
51        </intent>
52        <intent>
52-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:31:9-35:18
53            <action android:name="android.intent.action.VIEW" />
53-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
53-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
54
55            <data android:scheme="market" />
55-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
55-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
56        </intent>
57
58        <package android:name="com.android.chrome" />
58-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:9-54
58-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:12:18-51
59        <package android:name="com.google.android.webview" />
59-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:9-62
59-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:13:18-59
60        <package android:name="com.android.webview" />
60-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:9-55
60-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:14:18-52
61        <package android:name="com.android.vending" /> <!-- End of browser content -->
61-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:9-55
61-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:10:18-52
62        <!-- For CustomTabsService -->
63        <intent>
63-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:47:9-49:18
64            <action android:name="android.support.customtabs.action.CustomTabsService" />
64-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:13-90
64-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:48:21-87
65        </intent> <!-- End of CustomTabsService -->
66        <!-- For MRAID capabilities -->
67        <intent>
67-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:52:9-56:18
68            <action android:name="android.intent.action.INSERT" />
68-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:13-67
68-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:53:21-64
69
70            <data android:mimeType="vnd.android.cursor.dir/event" />
70-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
71        </intent>
72        <intent>
72-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:57:9-61:18
73            <action android:name="android.intent.action.VIEW" />
73-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
73-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
74
75            <data android:scheme="sms" />
75-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
75-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
76        </intent>
77        <intent>
77-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:62:9-66:18
78            <action android:name="android.intent.action.DIAL" />
78-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:13-65
78-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:63:21-62
79
80            <data android:path="tel:" />
80-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
81        </intent>
82        <intent>
82-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:8:9-10:18
83            <action android:name="com.applovin.am.intent.action.APPHUB_SERVICE" />
83-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:13-83
83-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:9:21-80
84        </intent>
85        <intent>
85-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:18:9-22:18
86            <action android:name="android.intent.action.ACTION_VIEW" />
86-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:13-72
86-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:19:21-69
87
88            <data android:scheme="https" />
88-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
88-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
89        </intent>
90
91        <package android:name="com.facebook.katana" />
91-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
91-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
92
93        <intent>
93-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:15:9-17:18
94            <action android:name="com.digitalturbine.ignite.cl.IgniteRemoteService" />
94-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:13-87
94-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:16:21-84
95        </intent>
96        <intent>
96-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:12:9-14:18
97            <action android:name="android.intent.action.MAIN" />
97-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
97-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
98        </intent>
99        <intent>
99-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:15:9-17:18
100            <action android:name="android.intent.action.VIEW" />
100-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
100-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
101        </intent>
102    </queries>
103
104    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
104-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:17:5-83
104-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:17:22-80
105    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
105-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:18:5-88
105-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:18:22-85
106    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
106-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:5-82
106-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:27:22-79
107    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
107-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
107-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
108    <uses-permission android:name="com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE" />
108-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:5-96
108-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:20:22-93
109
110    <permission
110-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
111        android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
111-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
112        android:protectionLevel="signature" />
112-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
113
114    <uses-permission android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
114-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
114-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
115
116    <application
116-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-132:19
117        android:name="com.tqhit.battery.one.BatteryApplication"
117-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
118        android:allowBackup="true"
118-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
119        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
119-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4912bc3b25b094e82d07c57850b5737f\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
120        android:dataExtractionRules="@xml/data_extraction_rules"
120-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
121        android:debuggable="true"
122        android:extractNativeLibs="false"
123        android:fullBackupContent="@xml/backup_rules"
123-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
124        android:hardwareAccelerated="true"
124-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:15:18-52
125        android:icon="@mipmap/ic_launcher"
125-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
126        android:label="@string/app_name"
126-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
127        android:roundIcon="@mipmap/ic_launcher_round"
127-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
128        android:screenOrientation="portrait"
128-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
129        android:supportsRtl="true"
129-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
130        android:theme="@style/Theme.BatteryOne" >
130-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
131        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
132        <meta-data
132-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-37:70
133            android:name="com.google.android.gms.ads.APPLICATION_ID"
133-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:36:13-69
134            android:value="ca-app-pub-9844172086883515~3386117176" />
134-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:37:13-67
135
136        <activity
136-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:9-47:20
137            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
137-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-80
138            android:exported="true"
138-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:41:13-36
139            android:theme="@style/Theme.BatteryOne.Splash" >
139-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:13-59
140            <intent-filter>
140-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-46:29
141                <action android:name="android.intent.action.MAIN" />
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
142
143                <category android:name="android.intent.category.LAUNCHER" />
143-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:17-77
143-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:27-74
144            </intent-filter>
145        </activity>
146        <activity
146-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:49:9-53:54
147            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
147-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:50:13-84
148            android:exported="false"
148-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:51:13-37
149            android:screenOrientation="portrait"
149-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:13-49
150            android:theme="@style/Theme.BatteryOne" />
150-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-52
151        <activity
151-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:9-59:51
152            android:name="com.tqhit.battery.one.activity.main.MainActivity"
152-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-76
153            android:exported="true"
153-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:57:13-36
154            android:launchMode="singleTask"
154-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:13-44
155            android:screenOrientation="portrait" />
155-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-49
156        <activity
156-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:9-65:51
157            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
157-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-86
158            android:configChanges="orientation|screenSize"
158-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:63:13-59
159            android:launchMode="singleTask"
159-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:13-44
160            android:screenOrientation="portrait" />
160-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-49
161        <activity
161-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:9-73:51
162            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
162-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-90
163            android:configChanges="orientation|screenSize"
163-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-59
164            android:exported="false"
164-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:69:13-37
165            android:launchMode="singleTask"
165-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:72:13-44
166            android:screenOrientation="portrait"
166-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:13-49
167            android:theme="@style/Theme.AppCompat.NoActionBar" />
167-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:13-63
168        <activity
168-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:9-81:51
169            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
169-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-89
170            android:configChanges="orientation|screenSize"
170-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-59
171            android:exported="false"
171-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:77:13-37
172            android:launchMode="singleTask"
172-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:80:13-44
173            android:screenOrientation="portrait"
173-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:13-49
174            android:theme="@style/Theme.AppCompat.NoActionBar" />
174-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:13-63
175        <activity
175-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:83:9-94:20
176            android:name="com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity"
176-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:13-110
177            android:exported="true"
177-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-36
178            android:screenOrientation="portrait"
178-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-49
179            android:theme="@style/Theme.AppCompat" >
179-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-51
180            <intent-filter>
180-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:88:13-93:29
181                <action android:name="android.intent.action.VIEW" />
181-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
181-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
182
183                <category android:name="android.intent.category.DEFAULT" />
183-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
183-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
184                <category android:name="android.intent.category.BROWSABLE" />
184-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
184-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
185
186                <data
186-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
187                    android:host="test_discharge"
187-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:48-77
188                    android:scheme="battery" />
188-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
189            </intent-filter>
190        </activity> <!-- Legacy TestNewChargeActivity removed - was in legacy directory -->
191        <!-- DebugActivity will be conditionally included via build variant manifests -->
192        <service
192-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:100:9-104:59
193            android:name="com.tqhit.battery.one.service.BatteryMonitorService"
193-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:101:13-79
194            android:enabled="true"
194-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:102:13-35
195            android:exported="false"
195-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:103:13-37
196            android:foregroundServiceType="specialUse" />
196-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:104:13-55
197        <service
197-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:105:9-109:59
198            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
198-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:106:13-80
199            android:enabled="true"
199-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:107:13-35
200            android:exported="false"
200-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:13-37
201            android:foregroundServiceType="specialUse" /> <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
201-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-55
202        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
203        <service
203-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:9-116:59
204            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
204-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:13-112
205            android:enabled="true"
205-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-35
206            android:exported="false"
206-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-37
207            android:foregroundServiceType="specialUse" />
207-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:116:13-55
208        <service
208-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:117:9-121:59
209            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
209-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:13-108
210            android:enabled="true"
210-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-35
211            android:exported="false"
211-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-37
212            android:foregroundServiceType="specialUse" />
212-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-55
213        <service
213-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:9-126:59
214            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
214-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:123:13-112
215            android:enabled="true"
215-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:124:13-35
216            android:exported="false"
216-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:13-37
217            android:foregroundServiceType="specialUse" />
217-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-55
218        <service
218-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:127:9-131:59
219            android:name="com.tqhit.battery.one.service.optimization.MemoryOptimizationService"
219-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:128:13-96
220            android:enabled="true"
220-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:129:13-35
221            android:exported="false"
221-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:130:13-37
222            android:foregroundServiceType="specialUse" /> <!-- HYPR Activities -->
222-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:131:13-55
223        <activity
223-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:13:9-19:59
224            android:name="com.hyprmx.android.sdk.activity.HyprMXOfferViewerActivity"
224-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:14:13-85
225            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
225-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:15:13-113
226            android:hardwareAccelerated="true"
226-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:16:13-47
227            android:label="HyprMX SDK"
227-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:17:13-39
228            android:launchMode="singleTop"
228-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:18:13-43
229            android:theme="@style/hyprmx_ActivityTheme" />
229-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:19:13-56
230        <activity
230-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:20:9-23:63
231            android:name="com.hyprmx.android.sdk.activity.HyprMXRequiredInformationActivity"
231-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:21:13-93
232            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
232-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:22:13-113
233            android:theme="@style/hyprmx_RequiredInfoTheme" />
233-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:23:13-60
234        <activity
234-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:24:9-27:59
235            android:name="com.hyprmx.android.sdk.activity.HyprMXNoOffersActivity"
235-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:25:13-82
236            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
236-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:26:13-113
237            android:theme="@style/hyprmx_ActivityTheme" />
237-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:27:13-56
238        <activity
238-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:28:9-32:59
239            android:name="com.hyprmx.android.sdk.overlay.HyprMXBrowserActivity"
239-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:29:13-80
240            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
240-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:30:13-113
241            android:hardwareAccelerated="true"
241-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:31:13-47
242            android:theme="@style/hyprmx_ActivityTheme" /> <!-- XENOSS core -->
242-->[com.hyprmx.android:HyprMX-SDK:6.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a9dfb15f18cd40273abd7cd5a9d115\transformed\jetified-HyprMX-SDK-6.4.2\AndroidManifest.xml:32:13-56
243        <!-- android:initOrder="-**********" Try to initialize after all the rest of content providers -->
244        <provider
244-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:38:9-47:20
245            android:name="androidx.startup.InitializationProvider"
245-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:39:13-67
246            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.androidx-startup"
246-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:40:13-68
247            android:exported="false"
247-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:41:13-37
248            android:initOrder="-**********" >
248-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:42:13-44
249            <meta-data
249-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:44:13-46:52
250                android:name="com.moloco.sdk.internal.android_context.StartupComponentInitialization"
250-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:45:17-102
251                android:value="androidx.startup" />
251-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:46:17-49
252            <meta-data
252-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:56:13-58:52
253                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
253-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:57:17-78
254                android:value="androidx.startup" />
254-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:58:17-49
255            <meta-data
255-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:59:13-61:52
256                android:name="com.unity3d.services.core.configuration.AdsSdkInitializer"
256-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:60:17-89
257                android:value="androidx.startup" />
257-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:61:17-49
258            <meta-data
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
259                android:name="androidx.emoji2.text.EmojiCompatInitializer"
259-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
260                android:value="androidx.startup" />
260-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f6f85e0cd56dd7a88a47a32dc125429\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
261            <meta-data
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
262                android:name="androidx.work.WorkManagerInitializer"
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
263                android:value="androidx.startup" />
263-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
264            <meta-data
264-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
265                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
265-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
266                android:value="androidx.startup" />
266-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
267        </provider> <!-- XENOSS renderer -->
268        <activity
268-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:51:9-56:20
269            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.templates.renderer.fullscreen.FullscreenWebviewActivity"
269-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:52:13-143
270            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
270-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:53:13-87
271            android:exported="false"
271-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:54:13-37
272            android:theme="@style/FullscreenAdActivity" >
272-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:55:13-56
273        </activity>
274        <activity
274-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:57:9-62:59
275            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.vast.VastActivity"
275-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:58:13-105
276            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
276-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:59:13-87
277            android:exported="false"
277-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:60:13-37
278            android:screenOrientation="fullSensor"
278-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:61:13-51
279            android:theme="@style/FullscreenAdActivity" />
279-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:62:13-56
280        <activity
280-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:63:9-68:59
281            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.staticrenderer.StaticAdActivity"
281-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:64:13-119
282            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
282-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:65:13-87
283            android:exported="false"
283-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:66:13-37
284            android:screenOrientation="fullSensor"
284-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:67:13-51
285            android:theme="@style/FullscreenAdActivity" />
285-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:68:13-56
286        <activity
286-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:69:9-74:59
287            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.mraid.MraidActivity"
287-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:70:13-107
288            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
288-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:71:13-87
289            android:exported="false"
289-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:72:13-37
290            android:screenOrientation="fullSensor"
290-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:73:13-51
291            android:theme="@style/FullscreenAdActivity" />
291-->[com.moloco.sdk:moloco-sdk:3.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf07240a31c0a7a4ab6735587246a0b2\transformed\jetified-moloco-sdk-3.10.0\AndroidManifest.xml:74:13-56
292        <activity
292-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
293            android:name="cat.ereza.customactivityoncrash.activity.DefaultErrorActivity"
293-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
294            android:process=":error_activity" />
294-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
295
296        <provider
296-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
297            android:name="cat.ereza.customactivityoncrash.provider.CaocInitProvider"
297-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
298            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.customactivityoncrashinitprovider"
298-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
299            android:exported="false"
299-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
300            android:initOrder="101" />
300-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f67424923db5eb4692c1e493ebde3158\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
301
302        <activity
302-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:14:9-19:46
303            android:name="com.yandex.mobile.ads.common.AdActivity"
303-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:15:13-67
304            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
304-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:16:13-122
305            android:enableOnBackInvokedCallback="false"
305-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:17:13-56
306            android:theme="@style/MonetizationAdsInternal.AdActivity" />
306-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:18:13-70
307
308        <provider
308-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:21:9-24:40
309            android:name="com.yandex.mobile.ads.core.initializer.MobileAdsInitializeProvider"
309-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:22:13-94
310            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.MobileAdsInitializeProvider"
310-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:23:13-79
311            android:exported="false" />
311-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:24:13-37
312
313        <activity
313-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:26:9-31:46
314            android:name="com.yandex.mobile.ads.features.debugpanel.ui.IntegrationInspectorActivity"
314-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:27:13-101
315            android:enableOnBackInvokedCallback="false"
315-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:28:13-56
316            android:exported="false"
316-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:29:13-37
317            android:theme="@style/DebugPanelTheme" />
317-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:30:13-51
318
319        <provider
319-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:33:9-41:20
320            android:name="com.yandex.mobile.ads.features.debugpanel.data.local.DebugPanelFileProvider"
320-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:34:13-103
321            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.monetization.ads.inspector.fileprovider"
321-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:35:13-91
322            android:exported="false"
322-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:36:13-37
323            android:grantUriPermissions="true" >
323-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:37:13-47
324            <meta-data
324-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:38:13-40:66
325                android:name="android.support.FILE_PROVIDER_PATHS"
325-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:39:17-67
326                android:resource="@xml/debug_panel_file_paths" />
326-->[com.yandex.android:mobileads:7.14.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65a6a6823d98194731daa68a1b3ace70\transformed\jetified-mobileads-7.14.1\AndroidManifest.xml:40:17-63
327        </provider>
328
329        <activity
329-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:21:9-26:74
330            android:name="com.unity3d.services.ads.adunit.AdUnitActivity"
330-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:22:13-74
331            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
331-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:23:13-170
332            android:exported="false"
332-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:24:13-37
333            android:hardwareAccelerated="true"
333-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:25:13-47
334            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
334-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:26:13-71
335        <activity
335-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:27:9-32:86
336            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentActivity"
336-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:28:13-85
337            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
337-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:29:13-170
338            android:exported="false"
338-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:30:13-37
339            android:hardwareAccelerated="true"
339-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:31:13-47
340            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
340-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:32:13-83
341        <activity
341-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:33:9-38:86
342            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity"
342-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:34:13-93
343            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
343-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:35:13-170
344            android:exported="false"
344-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:36:13-37
345            android:hardwareAccelerated="false"
345-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:37:13-48
346            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
346-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:38:13-83
347        <activity
347-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:39:9-44:74
348            android:name="com.unity3d.services.ads.adunit.AdUnitSoftwareActivity"
348-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:40:13-82
349            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
349-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:41:13-170
350            android:exported="false"
350-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:42:13-37
351            android:hardwareAccelerated="false"
351-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:43:13-48
352            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
352-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:44:13-71
353        <activity
353-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:45:9-50:74
354            android:name="com.unity3d.ads.adplayer.FullScreenWebViewDisplay"
354-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:46:13-77
355            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
355-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:47:13-170
356            android:exported="false"
356-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:48:13-37
357            android:hardwareAccelerated="true"
357-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:49:13-47
358            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
358-->[com.unity3d.ads:unity-ads:4.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\211a855e0011d900b875190f4ce55f66\transformed\jetified-unity-ads-4.15.0\AndroidManifest.xml:50:13-71
359        <activity
359-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:39:9-44:46
360            android:name="com.inmobi.ads.rendering.InMobiAdActivity"
360-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:40:13-69
361            android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout|locale|fontScale|uiMode"
361-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:41:13-139
362            android:hardwareAccelerated="true"
362-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:42:13-47
363            android:theme="@android:style/Theme.NoTitleBar" />
363-->[com.inmobi.monetization:inmobi-ads-kotlin:10.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0fb5cde1755c2ebdaf0e87e6ccee830a\transformed\jetified-inmobi-ads-kotlin-10.8.3\AndroidManifest.xml:43:13-60
364
365        <meta-data
365-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:26:9-28:39
366            android:name="com.bytedance.sdk.pangle.version"
366-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:27:13-60
367            android:value="7.2.0.6" /> <!-- 下面的activity和service必须注册 -->
367-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:28:13-36
368        <activity
368-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:30:9-33:45
369            android:name="com.bytedance.sdk.openadsdk.activity.TTCeilingLandingPageActivity"
369-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:31:13-93
370            android:configChanges="keyboardHidden|orientation|screenSize"
370-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:32:13-74
371            android:launchMode="standard" />
371-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:33:13-42
372        <activity
372-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:34:9-38:54
373            android:name="com.bytedance.sdk.openadsdk.activity.TTLandingPageActivity"
373-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:35:13-86
374            android:configChanges="keyboardHidden|orientation|screenSize"
374-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:36:13-74
375            android:launchMode="standard"
375-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:37:13-42
376            android:theme="@style/tt_landing_page" />
376-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:38:13-51
377        <activity
377-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:39:9-43:54
378            android:name="com.bytedance.sdk.openadsdk.activity.TTPlayableLandingPageActivity"
378-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:40:13-94
379            android:configChanges="keyboardHidden|orientation|screenSize"
379-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:41:13-74
380            android:launchMode="standard"
380-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:42:13-42
381            android:theme="@style/tt_landing_page" />
381-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:43:13-51
382        <activity
382-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:44:9-48:54
383            android:name="com.bytedance.sdk.openadsdk.activity.TTVideoLandingPageLink2Activity"
383-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:45:13-96
384            android:configChanges="keyboardHidden|orientation|screenSize"
384-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:46:13-74
385            android:launchMode="standard"
385-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:47:13-42
386            android:theme="@style/tt_landing_page" />
386-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:48:13-51
387        <activity
387-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:49:9-52:75
388            android:name="com.bytedance.sdk.openadsdk.activity.TTDelegateActivity"
388-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:50:13-83
389            android:launchMode="standard"
389-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:51:13-42
390            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
390-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:52:13-72
391        <activity
391-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:53:9-57:62
392            android:name="com.bytedance.sdk.openadsdk.activity.TTWebsiteActivity"
392-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:54:13-82
393            android:launchMode="standard"
393-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:55:13-42
394            android:screenOrientation="portrait"
394-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:56:13-49
395            android:theme="@style/tt_privacy_landing_page" />
395-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:57:13-59
396
397        <service android:name="com.bytedance.sdk.openadsdk.multipro.aidl.BinderPoolService" />
397-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:9-95
397-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:59:18-92
398
399        <activity
399-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:61:9-65:66
400            android:name="com.bytedance.sdk.openadsdk.activity.TTAppOpenAdActivity"
400-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:62:13-84
401            android:configChanges="keyboardHidden|orientation|screenSize"
401-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:63:13-74
402            android:launchMode="standard"
402-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:64:13-42
403            android:theme="@style/tt_app_open_ad_no_animation" />
403-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:65:13-63
404        <activity
404-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:66:9-70:57
405            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardVideoActivity"
405-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:67:13-86
406            android:configChanges="keyboardHidden|orientation|screenSize"
406-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:68:13-74
407            android:launchMode="standard"
407-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:69:13-42
408            android:theme="@style/tt_full_screen_new" />
408-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:70:13-54
409        <activity
409-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:71:9-75:57
410            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardExpressVideoActivity"
410-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:72:13-93
411            android:configChanges="keyboardHidden|orientation|screenSize"
411-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:73:13-74
412            android:launchMode="standard"
412-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:74:13-42
413            android:theme="@style/tt_full_screen_new" />
413-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:75:13-54
414        <activity
414-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:76:9-80:57
415            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenVideoActivity"
415-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:77:13-90
416            android:configChanges="keyboardHidden|orientation|screenSize"
416-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:78:13-74
417            android:launchMode="standard"
417-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:79:13-42
418            android:theme="@style/tt_full_screen_new" />
418-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:80:13-54
419        <activity
419-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:81:9-85:57
420            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenExpressVideoActivity"
420-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:82:13-97
421            android:configChanges="keyboardHidden|orientation|screenSize"
421-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:83:13-74
422            android:launchMode="standard"
422-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:84:13-42
423            android:theme="@style/tt_full_screen_new" />
423-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:85:13-54
424        <activity
424-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:86:9-90:65
425            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialActivity"
425-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:87:13-87
426            android:configChanges="keyboardHidden|orientation|screenSize"
426-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:88:13-74
427            android:launchMode="standard"
427-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:89:13-42
428            android:theme="@style/tt_full_screen_interaction" />
428-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:90:13-62
429        <activity
429-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:91:9-95:65
430            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialExpressActivity"
430-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:92:13-94
431            android:configChanges="keyboardHidden|orientation|screenSize"
431-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:93:13-74
432            android:launchMode="standard"
432-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:94:13-42
433            android:theme="@style/tt_full_screen_interaction" />
433-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:95:13-62
434        <activity
434-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:96:9-100:57
435            android:name="com.bytedance.sdk.openadsdk.activity.TTAdActivity"
435-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:97:13-77
436            android:configChanges="keyboardHidden|orientation|screenSize"
436-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:98:13-74
437            android:launchMode="standard"
437-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:99:13-42
438            android:theme="@style/tt_full_screen_new" />
438-->[com.pangle.global:pag-sdk-ad:unfat-7206-20250616114438] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1575b72cf39be0513007f700cf9dbd85\transformed\jetified-pag-sdk-ad-unfat-7206-20250616114438\AndroidManifest.xml:100:13-54
439
440        <provider
440-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:18:9-22:39
441            android:name="io.bidmachine.BidMachineInitProvider"
441-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:19:13-64
442            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.bidmachineinitprovider"
442-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:20:13-74
443            android:exported="false"
443-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:21:13-37
444            android:initOrder="100" />
444-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:22:13-36
445
446        <activity
446-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:24:9-26:80
447            android:name="io.bidmachine.nativead.view.VideoPlayerActivity"
447-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:25:13-75
448            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
448-->[io.bidmachine:ads:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc5e7a27dca1b966437e3e333896d6ad\transformed\jetified-ads-3.3.0\AndroidManifest.xml:26:13-77
449        <activity
449-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:29:9-33:74
450            android:name="io.bidmachine.rendering.ad.fullscreen.FullScreenActivity"
450-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:30:13-84
451            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
451-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:31:13-122
452            android:hardwareAccelerated="true"
452-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:32:13-47
453            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
453-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:33:13-71
454        <activity
454-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:34:9-38:74
455            android:name="io.bidmachine.iab.mraid.MraidActivity"
455-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:35:13-65
456            android:configChanges="keyboardHidden|orientation|screenSize"
456-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:36:13-74
457            android:hardwareAccelerated="true"
457-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:37:13-47
458            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
458-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:38:13-71
459        <activity
459-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:39:9-43:74
460            android:name="io.bidmachine.iab.vast.activity.VastActivity"
460-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:40:13-72
461            android:configChanges="keyboardHidden|orientation|screenSize"
461-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:41:13-74
462            android:hardwareAccelerated="true"
462-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:42:13-47
463            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
463-->[io.bidmachine:rendering:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\282e75bd3a0a6df0baa7a2c932947347\transformed\jetified-rendering-2.5.2\AndroidManifest.xml:43:13-71
464        <activity
464-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:73:9-78:43
465            android:name="com.google.android.gms.ads.AdActivity"
465-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:74:13-65
466            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
466-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:75:13-122
467            android:exported="false"
467-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:76:13-37
468            android:theme="@android:style/Theme.Translucent" />
468-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:77:13-61
469
470        <provider
470-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:80:9-85:43
471            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
471-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:81:13-76
472            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.mobileadsinitprovider"
472-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:82:13-73
473            android:exported="false"
473-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:83:13-37
474            android:initOrder="100" />
474-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:84:13-36
475
476        <service
476-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:87:9-91:43
477            android:name="com.google.android.gms.ads.AdService"
477-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:88:13-64
478            android:enabled="true"
478-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:89:13-35
479            android:exported="false" />
479-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:90:13-37
480
481        <activity
481-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:93:9-97:43
482            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
482-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:94:13-82
483            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
483-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:95:13-122
484            android:exported="false" />
484-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:96:13-37
485        <activity
485-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:98:9-105:43
486            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
486-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:99:13-82
487            android:excludeFromRecents="true"
487-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:100:13-46
488            android:exported="false"
488-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:101:13-37
489            android:launchMode="singleTask"
489-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:102:13-44
490            android:taskAffinity=""
490-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:103:13-36
491            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
491-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:104:13-72
492
493        <meta-data
493-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:107:9-109:36
494            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
494-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:108:13-79
495            android:value="true" />
495-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:109:13-33
496        <meta-data
496-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:110:9-112:36
497            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
497-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:111:13-83
498            android:value="true" />
498-->[com.google.android.gms:play-services-ads-api:24.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94d994848d64bd33dd51b02a9008bf52\transformed\jetified-play-services-ads-api-24.4.0\AndroidManifest.xml:112:13-33
499
500        <receiver
500-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
501            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
501-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
502            android:enabled="true"
502-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
503            android:exported="false" >
503-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
504        </receiver>
505
506        <service
506-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
507            android:name="com.google.android.gms.measurement.AppMeasurementService"
507-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
508            android:enabled="true"
508-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
509            android:exported="false" />
509-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
510        <service
510-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
511            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
511-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
512            android:enabled="true"
512-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
513            android:exported="false"
513-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
514            android:permission="android.permission.BIND_JOB_SERVICE" />
514-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8697f49cd5836ef4d72d5e2977e2f6c9\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
515        <service
515-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
516            android:name="com.google.firebase.components.ComponentDiscoveryService"
516-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
517            android:directBootAware="true"
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
518            android:exported="false" >
518-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
519            <meta-data
519-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
520                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
520-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
521                android:value="com.google.firebase.components.ComponentRegistrar" />
521-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e5c4615e1c75041d0f7e52abaf10668\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
522            <meta-data
522-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
523                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
523-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
524                android:value="com.google.firebase.components.ComponentRegistrar" />
524-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
525            <meta-data
525-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
526                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
526-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
527                android:value="com.google.firebase.components.ComponentRegistrar" />
527-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bdff9550d1a44ce691c5420d70c92ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
528            <meta-data
528-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
529                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
529-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
530                android:value="com.google.firebase.components.ComponentRegistrar" />
530-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
531            <meta-data
531-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
532                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
532-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
533                android:value="com.google.firebase.components.ComponentRegistrar" />
533-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68cd766186cf37457d049bbfbafb3e1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
534            <meta-data
534-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
535                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
535-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
536                android:value="com.google.firebase.components.ComponentRegistrar" />
536-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
537            <meta-data
537-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
538                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
538-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
539                android:value="com.google.firebase.components.ComponentRegistrar" />
539-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
540            <meta-data
540-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
541                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
541-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
542                android:value="com.google.firebase.components.ComponentRegistrar" />
542-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96798ee0c8cb042651b1b30786641696\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
543            <meta-data
543-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
544                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
544-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
545                android:value="com.google.firebase.components.ComponentRegistrar" />
545-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1fa5358b2ca55d64e9dc66e29258bd2\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
546            <meta-data
546-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
547                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
547-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
548                android:value="com.google.firebase.components.ComponentRegistrar" />
548-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
549            <meta-data
549-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
550                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
550-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
551                android:value="com.google.firebase.components.ComponentRegistrar" />
551-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d11a4b6165811d5c7657a5cefe8ea76\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
552            <meta-data
552-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
553                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
553-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
554                android:value="com.google.firebase.components.ComponentRegistrar" />
554-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5ceb6f09f2e732effc045e766faae53\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
555        </service>
556
557        <provider
557-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:26:9-30:39
558            android:name="com.applovin.sdk.AppLovinInitProvider"
558-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:27:13-65
559            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.applovininitprovider"
559-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:28:13-72
560            android:exported="false"
560-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:29:13-37
561            android:initOrder="101" /> <!-- Init order is 101 so we're before Firebase/Google which uses 100 -->
561-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:30:13-36
562        <activity
562-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:32:9-39:74
563            android:name="com.applovin.adview.AppLovinFullscreenActivity"
563-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:33:13-74
564            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
564-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:34:13-139
565            android:exported="false"
565-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:35:13-37
566            android:hardwareAccelerated="true"
566-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:36:13-47
567            android:launchMode="singleTop"
567-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:37:13-43
568            android:screenOrientation="behind"
568-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:38:13-47
569            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
569-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:39:13-71
570        <activity
570-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:40:9-42:142
571            android:name="com.applovin.sdk.AppLovinWebViewActivity"
571-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:41:13-68
572            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" /> <!-- Mediation Debugger Activities -->
572-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:42:13-139
573        <activity
573-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:45:9-48:87
574            android:name="com.applovin.mediation.MaxDebuggerActivity"
574-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:46:13-70
575            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
575-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:47:13-139
576            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
576-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:48:13-84
577        <activity
577-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:49:9-52:87
578            android:name="com.applovin.mediation.MaxDebuggerDetailActivity"
578-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:50:13-76
579            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
579-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:51:13-139
580            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
580-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:52:13-84
581        <activity
581-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:53:9-56:87
582            android:name="com.applovin.mediation.MaxDebuggerMultiAdActivity"
582-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:54:13-77
583            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
583-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:55:13-139
584            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
584-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:56:13-84
585        <activity
585-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:57:9-60:87
586            android:name="com.applovin.mediation.MaxDebuggerAdUnitsListActivity"
586-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:58:13-81
587            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
587-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:59:13-139
588            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
588-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:60:13-84
589        <activity
589-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:61:9-64:87
590            android:name="com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity"
590-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:62:13-90
591            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
591-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:63:13-139
592            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
592-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:64:13-84
593        <activity
593-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:65:9-68:87
594            android:name="com.applovin.mediation.MaxDebuggerAdUnitDetailActivity"
594-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:66:13-82
595            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
595-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:67:13-139
596            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
596-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:68:13-84
597        <activity
597-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:69:9-72:87
598            android:name="com.applovin.mediation.MaxDebuggerCmpNetworksListActivity"
598-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:70:13-85
599            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
599-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:71:13-139
600            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
600-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:72:13-84
601        <activity
601-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:73:9-76:87
602            android:name="com.applovin.mediation.MaxDebuggerTcfConsentStatusesListActivity"
602-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:74:13-92
603            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
603-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:75:13-139
604            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
604-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:76:13-84
605        <activity
605-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:77:9-80:87
606            android:name="com.applovin.mediation.MaxDebuggerTcfInfoListActivity"
606-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:78:13-81
607            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
607-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:79:13-139
608            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
608-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:80:13-84
609        <activity
609-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:81:9-84:87
610            android:name="com.applovin.mediation.MaxDebuggerTcfStringActivity"
610-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:82:13-79
611            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
611-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:83:13-139
612            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
612-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:84:13-84
613        <activity
613-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:85:9-88:87
614            android:name="com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity"
614-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:86:13-85
615            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
615-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:87:13-139
616            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
616-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:88:13-84
617        <activity
617-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:89:9-92:87
618            android:name="com.applovin.mediation.MaxDebuggerTestModeNetworkActivity"
618-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:90:13-85
619            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
619-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:91:13-139
620            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
620-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:92:13-84
621        <activity
621-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:93:9-96:87
622            android:name="com.applovin.mediation.MaxDebuggerUnifiedFlowActivity"
622-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:94:13-81
623            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
623-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:95:13-139
624            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
624-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:96:13-84
625        <activity
625-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:97:9-100:87
626            android:name="com.applovin.mediation.MaxDebuggerWaterfallSegmentsActivity"
626-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:98:13-87
627            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
627-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:99:13-139
628            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
628-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:100:13-84
629        <activity
629-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:101:9-104:91
630            android:name="com.applovin.creative.MaxCreativeDebuggerActivity"
630-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:102:13-77
631            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
631-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:103:13-139
632            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" />
632-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:104:13-88
633        <activity
633-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:105:9-108:91
634            android:name="com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity"
634-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:106:13-88
635            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
635-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:107:13-139
636            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" /> <!-- Services -->
636-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:108:13-88
637        <service
637-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:111:9-114:44
638            android:name="com.applovin.impl.adview.activity.FullscreenAdService"
638-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:112:13-81
639            android:exported="false"
639-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:113:13-37
640            android:stopWithTask="false" />
640-->[com.applovin:applovin-sdk:13.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d148dadd36a9aeec86630cf6fa0b2687\transformed\jetified-applovin-sdk-13.3.1\AndroidManifest.xml:114:13-41
641
642        <activity
642-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:26:9-33:80
643            android:name="com.chartboost.sdk.view.CBImpressionActivity"
643-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:27:13-72
644            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
644-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:28:13-106
645            android:enableOnBackInvokedCallback="true"
645-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:29:13-55
646            android:excludeFromRecents="true"
646-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:30:13-46
647            android:exported="false"
647-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:31:13-37
648            android:hardwareAccelerated="true"
648-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:32:13-47
649            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
649-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:33:13-77
650        <activity
650-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:34:9-40:86
651            android:name="com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity"
651-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:35:13-92
652            android:configChanges="keyboardHidden|orientation|screenSize"
652-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:36:13-74
653            android:excludeFromRecents="true"
653-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:37:13-46
654            android:exported="false"
654-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:38:13-37
655            android:hardwareAccelerated="true"
655-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:39:13-47
656            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> <!-- ExoPlayer DownloadService -->
656-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:40:13-83
657        <service
657-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:43:9-53:19
658            android:name="com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService"
658-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:44:13-113
659            android:exported="false" >
659-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:45:13-37
660
661            <!-- This is needed for Scheduler -->
662            <intent-filter>
662-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:48:13-52:29
663                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />
663-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:17-102
663-->[com.chartboost:chartboost-sdk:9.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a6a3410bbbd199bffb46da1eed6d5c0\transformed\jetified-chartboost-sdk-9.8.3\AndroidManifest.xml:49:25-99
664
665                <category android:name="android.intent.category.DEFAULT" />
665-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
665-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
666            </intent-filter>
667        </service> <!-- AppMetrica Analytics: common service -->
668        <service
668-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:15:9-26:19
669            android:name="io.appmetrica.analytics.internal.AppMetricaService"
669-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:16:13-78
670            android:enabled="true"
670-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:17:13-35
671            android:exported="false" >
671-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:18:13-37
672            <intent-filter>
672-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:19:13-25:29
673                <category android:name="android.intent.category.DEFAULT" />
673-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
673-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
674
675                <action android:name="io.appmetrica.analytics.IAppMetricaService" />
675-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:17-85
675-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:22:25-82
676
677                <data android:scheme="appmetrica" />
677-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
677-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
678            </intent-filter>
679        </service> <!-- To track preinstallations -->
680        <provider
680-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:29:9-34:54
681            android:name="io.appmetrica.analytics.internal.PreloadInfoContentProvider"
681-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:30:13-87
682            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.appmetrica.preloadinfo.retail"
682-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:31:13-81
683            android:enabled="true"
683-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:32:13-35
684            android:exported="true" />
684-->[io.appmetrica.analytics:analytics:7.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4902841ca7a4747b85f1615765f400d\transformed\jetified-analytics-7.9.0\AndroidManifest.xml:33:13-36
685
686        <service
686-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
687            android:name="com.google.firebase.sessions.SessionLifecycleService"
687-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
688            android:enabled="true"
688-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
689            android:exported="false" />
689-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506a7d52c344aa96696d3f7fc0fbb75d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
690
691        <provider
691-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
692            android:name="com.google.firebase.provider.FirebaseInitProvider"
692-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
693            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.firebaseinitprovider"
693-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
694            android:directBootAware="true"
694-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
695            android:exported="false"
695-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
696            android:initOrder="100" />
696-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4bd069ca047af608afddeae4f49a58dc\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
697
698        <activity
698-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
699            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
699-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
700            android:excludeFromRecents="true"
700-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
701            android:exported="false"
701-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
702            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
702-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
703        <!--
704            Service handling Google Sign-In user revocation. For apps that do not integrate with
705            Google Sign-In, this service will never be started.
706        -->
707        <service
707-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
708            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
708-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
709            android:exported="true"
709-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
710            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
710-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
711            android:visibleToInstantApps="true" />
711-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d59c7051c2ec4e5368308061584615f\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
712
713        <activity
713-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
714            android:name="com.google.android.gms.common.api.GoogleApiActivity"
714-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
715            android:exported="false"
715-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
716            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
716-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8b1eaa684e277376481359ae9741c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
717
718        <uses-library
718-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
719            android:name="androidx.window.extensions"
719-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
720            android:required="false" />
720-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
721        <uses-library
721-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
722            android:name="androidx.window.sidecar"
722-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
723            android:required="false" />
723-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56b0ea17544daae48dbd121ce9ae567a\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
724
725        <service
725-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
726            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
726-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
727            android:directBootAware="false"
727-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
728            android:enabled="@bool/enable_system_alarm_service_default"
728-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
729            android:exported="false" />
729-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
730        <service
730-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
731            android:name="androidx.work.impl.background.systemjob.SystemJobService"
731-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
732            android:directBootAware="false"
732-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
733            android:enabled="@bool/enable_system_job_service_default"
733-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
734            android:exported="true"
734-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
735            android:permission="android.permission.BIND_JOB_SERVICE" />
735-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
736        <service
736-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
737            android:name="androidx.work.impl.foreground.SystemForegroundService"
737-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
738            android:directBootAware="false"
738-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
739            android:enabled="@bool/enable_system_foreground_service_default"
739-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
740            android:exported="false" />
740-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
741
742        <receiver
742-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
743            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
743-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
744            android:directBootAware="false"
744-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
745            android:enabled="true"
745-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
746            android:exported="false" />
746-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
747        <receiver
747-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
748            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
748-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
749            android:directBootAware="false"
749-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
750            android:enabled="false"
750-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
751            android:exported="false" >
751-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
752            <intent-filter>
752-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
753                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
753-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
753-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
754                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
754-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
754-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
755            </intent-filter>
756        </receiver>
757        <receiver
757-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
758            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
758-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
759            android:directBootAware="false"
759-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
760            android:enabled="false"
760-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
761            android:exported="false" >
761-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
762            <intent-filter>
762-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
763                <action android:name="android.intent.action.BATTERY_OKAY" />
763-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
763-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
764                <action android:name="android.intent.action.BATTERY_LOW" />
764-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
764-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
765            </intent-filter>
766        </receiver>
767        <receiver
767-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
768            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
768-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
769            android:directBootAware="false"
769-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
770            android:enabled="false"
770-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
771            android:exported="false" >
771-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
772            <intent-filter>
772-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
773                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
773-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
773-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
774                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
774-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
774-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
775            </intent-filter>
776        </receiver>
777        <receiver
777-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
778            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
778-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
779            android:directBootAware="false"
779-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
780            android:enabled="false"
780-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
781            android:exported="false" >
781-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
782            <intent-filter>
782-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
783                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
783-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
783-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
784            </intent-filter>
785        </receiver>
786        <receiver
786-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
787            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
787-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
788            android:directBootAware="false"
788-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
789            android:enabled="false"
789-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
790            android:exported="false" >
790-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
791            <intent-filter>
791-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
792                <action android:name="android.intent.action.BOOT_COMPLETED" />
792-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
792-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
793                <action android:name="android.intent.action.TIME_SET" />
793-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
793-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
794                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
794-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
794-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
795            </intent-filter>
796        </receiver>
797        <receiver
797-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
798            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
798-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
799            android:directBootAware="false"
799-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
800            android:enabled="@bool/enable_system_alarm_service_default"
800-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
801            android:exported="false" >
801-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
802            <intent-filter>
802-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
803                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
803-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
803-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
804            </intent-filter>
805        </receiver>
806        <receiver
806-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
807            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
807-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
808            android:directBootAware="false"
808-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
809            android:enabled="true"
809-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
810            android:exported="true"
810-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
811            android:permission="android.permission.DUMP" >
811-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
812            <intent-filter>
812-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
813                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
813-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
813-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
814            </intent-filter>
815        </receiver>
816
817        <uses-library
817-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
818            android:name="android.ext.adservices"
818-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
819            android:required="false" />
819-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79e63424ace2f8e7b3d5cf4517f24058\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
820
821        <activity
821-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
822            android:name="com.facebook.ads.AudienceNetworkActivity"
822-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
823            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
823-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
824            android:exported="false"
824-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
825            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
825-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
826
827        <provider
827-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
828            android:name="com.facebook.ads.AudienceNetworkContentProvider"
828-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
829            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.AudienceNetworkContentProvider"
829-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
830            android:exported="false" />
830-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1d587ea3febcfc3604c3852cd23fa2b\transformed\jetified-audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
831
832        <meta-data
832-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
833            android:name="com.google.android.gms.version"
833-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
834            android:value="@integer/google_play_services_version" />
834-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1057906e728c9c7b13b7392bddc5c3a4\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
835
836        <activity
836-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:16:9-20:46
837            android:name="com.vungle.ads.internal.ui.VungleActivity"
837-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:17:13-69
838            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
838-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:18:13-113
839            android:hardwareAccelerated="true"
839-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:19:13-47
840            android:launchMode="singleTop" />
840-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:20:13-43
841
842        <provider
842-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:22:9-26:39
843            android:name="com.vungle.ads.VungleProvider"
843-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:23:13-57
844            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.vungle-provider"
844-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:24:13-67
845            android:exported="false"
845-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:25:13-37
846            android:initOrder="102" />
846-->[com.vungle:vungle-ads:7.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b3edc39c51c6c03b2ea068357cb7ded\transformed\jetified-vungle-ads-7.5.0\AndroidManifest.xml:26:13-36
847
848        <service
848-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
849            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
849-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
850            android:exported="false" >
850-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
851            <meta-data
851-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
852                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
852-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
853                android:value="cct" />
853-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8e679e07a0cfd88272113ecd64163b6\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
854        </service>
855        <service
855-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:24:9-28:63
856            android:name="androidx.room.MultiInstanceInvalidationService"
856-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:25:13-74
857            android:directBootAware="true"
857-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:26:13-43
858            android:exported="false" />
858-->[androidx.room:room-runtime:2.5.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb5068817a9eda8dac863740290eab1\transformed\room-runtime-2.5.2\AndroidManifest.xml:27:13-37
859
860        <provider
860-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
861            android:name="com.squareup.picasso.PicassoProvider"
861-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
862            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.com.squareup.picasso"
862-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
863            android:exported="false" />
863-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19c1c1a6addea3a0a2e63b2487a2127f\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
864
865        <activity
865-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:12:9-16:63
866            android:name="com.ironsource.sdk.controller.ControllerActivity"
866-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:13:13-76
867            android:configChanges="orientation|screenSize"
867-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:14:13-59
868            android:hardwareAccelerated="true"
868-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:15:13-47
869            android:theme="@android:style/Theme.NoTitleBar" />
869-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:16:13-60
870        <activity
870-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:17:9-21:75
871            android:name="com.ironsource.sdk.controller.InterstitialActivity"
871-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:18:13-78
872            android:configChanges="orientation|screenSize"
872-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:19:13-59
873            android:hardwareAccelerated="true"
873-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:20:13-47
874            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
874-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:21:13-72
875        <activity
875-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:22:9-26:75
876            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
876-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:23:13-73
877            android:configChanges="orientation|screenSize"
877-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:24:13-59
878            android:hardwareAccelerated="true"
878-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:25:13-47
879            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
879-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:26:13-72
880        <activity
880-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:27:9-36:20
881            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
881-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:28:13-83
882            android:configChanges="orientation|screenSize"
882-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:29:13-59
883            android:exported="false"
883-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:30:13-37
884            android:hardwareAccelerated="true"
884-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:31:13-47
885            android:theme="@android:style/Theme.NoTitleBar" >
885-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:32:13-60
886            <meta-data
886-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:33:13-35:40
887                android:name="android.webkit.WebView.EnableSafeBrowsing"
887-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:34:17-73
888                android:value="true" />
888-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:35:17-37
889        </activity>
890
891        <provider
891-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:38:9-41:40
892            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
892-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:39:13-80
893            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.IronsourceLifecycleProvider"
893-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:40:13-79
894            android:exported="false" />
894-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:41:13-37
895        <provider
895-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:42:9-45:40
896            android:name="com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider"
896-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:43:13-87
897            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.LevelPlayActivityLifecycleProvider"
897-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:44:13-86
898            android:exported="false" />
898-->[com.unity3d.ads-mediation:mediation-sdk:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6824a39cc765b1f0ad403e69efbe996\transformed\jetified-mediation-sdk-8.9.1\AndroidManifest.xml:45:13-37
899
900        <receiver
900-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
901            android:name="androidx.profileinstaller.ProfileInstallReceiver"
901-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
902            android:directBootAware="false"
902-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
903            android:enabled="true"
903-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
904            android:exported="true"
904-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
905            android:permission="android.permission.DUMP" >
905-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
906            <intent-filter>
906-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
907                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
907-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
907-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
908            </intent-filter>
909            <intent-filter>
909-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
910                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
910-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
910-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
911            </intent-filter>
912            <intent-filter>
912-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
913                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
913-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
913-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
914            </intent-filter>
915            <intent-filter>
915-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
916                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
916-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
916-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5dc79f403e628c89e910ed9e70e893ba\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
917            </intent-filter>
918        </receiver>
919
920        <service
920-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
921            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
921-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
922            android:exported="false"
922-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
923            android:permission="android.permission.BIND_JOB_SERVICE" >
923-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
924        </service>
925
926        <receiver
926-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
927            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
927-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
928            android:exported="false" />
928-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87caeb76a8f454072692be4ac886daa9\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
929
930        <provider
930-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
931            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
931-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
932            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.adjust-lifecycle-provider"
932-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
933            android:exported="false" />
933-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a40ff80002b073731a5865390a522a43\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
934
935        <activity
935-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:20:9-23:52
936            android:name="sg.bigo.ads.ad.splash.AdSplashActivity"
936-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:21:13-66
937            android:screenOrientation="portrait"
937-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:22:13-49
938            android:theme="@android:style/Theme" />
938-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:23:13-49
939        <activity
939-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:24:9-27:52
940            android:name="sg.bigo.ads.ad.splash.LandscapeAdSplashActivity"
940-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:25:13-75
941            android:screenOrientation="landscape"
941-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:26:13-50
942            android:theme="@android:style/Theme" />
942-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:27:13-49
943
944        <provider
944-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:29:9-32:40
945            android:name="sg.bigo.ads.controller.provider.BigoAdsProvider"
945-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:30:13-75
946            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.BigoAdsProvider"
946-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:31:13-67
947            android:exported="false" />
947-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:32:13-37
948
949        <activity
949-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:34:9-36:55
950            android:name="sg.bigo.ads.controller.form.AdFormActivity"
950-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:35:13-70
951            android:windowSoftInputMode="adjustPan" />
951-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:36:13-52
952        <activity
952-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:37:9-43:20
953            android:name="sg.bigo.ads.api.AdActivity"
953-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:38:13-54
954            android:configChanges="orientation|screenSize"
954-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:39:13-59
955            android:screenOrientation="portrait"
955-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:40:13-49
956            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
956-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:41:13-72
957            android:windowSoftInputMode="stateAlwaysHidden" >
957-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:42:13-60
958        </activity>
959        <activity
959-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:44:9-50:20
960            android:name="sg.bigo.ads.api.PopupAdActivity"
960-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:45:13-59
961            android:configChanges="orientation|screenSize"
961-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:46:13-59
962            android:screenOrientation="portrait"
962-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:47:13-49
963            android:theme="@style/TransparentDialog"
963-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:48:13-53
964            android:windowSoftInputMode="stateAlwaysHidden" >
964-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:49:13-60
965        </activity>
966        <activity
966-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:51:9-57:20
967            android:name="sg.bigo.ads.api.LandingStyleableActivity"
967-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:52:13-68
968            android:configChanges="orientation|screenSize"
968-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:53:13-59
969            android:screenOrientation="behind"
969-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:54:13-47
970            android:theme="@android:style/Theme.Holo.Light.Dialog.NoActionBar"
970-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:55:13-79
971            android:windowSoftInputMode="stateAlwaysHidden" >
971-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:56:13-60
972        </activity>
973        <activity
973-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:58:9-64:20
974            android:name="sg.bigo.ads.api.LandscapeAdActivity"
974-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:59:13-63
975            android:configChanges="orientation|screenSize"
975-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:60:13-59
976            android:screenOrientation="landscape"
976-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:61:13-50
977            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
977-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:62:13-72
978            android:windowSoftInputMode="stateAlwaysHidden" >
978-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:63:13-60
979        </activity>
980        <activity
980-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:65:9-71:20
981            android:name="sg.bigo.ads.api.CompanionAdActivity"
981-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:66:13-63
982            android:configChanges="orientation|screenSize"
982-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:67:13-59
983            android:screenOrientation="portrait"
983-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:68:13-49
984            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
984-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:69:13-72
985            android:windowSoftInputMode="stateAlwaysHidden" >
985-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:70:13-60
986        </activity>
987        <activity
987-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:72:9-78:20
988            android:name="sg.bigo.ads.api.LandscapeCompanionAdActivity"
988-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:73:13-72
989            android:configChanges="orientation|screenSize"
989-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:74:13-59
990            android:screenOrientation="landscape"
990-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:75:13-50
991            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
991-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:76:13-72
992            android:windowSoftInputMode="stateAlwaysHidden" >
992-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:77:13-60
993        </activity>
994        <activity
994-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:79:9-83:74
995            android:name="sg.bigo.ads.core.mraid.MraidVideoActivity"
995-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:80:13-69
996            android:configChanges="keyboardHidden|orientation|screenSize"
996-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:81:13-74
997            android:screenOrientation="portrait"
997-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:82:13-49
998            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
998-->[com.bigossp:bigo-ads:5.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b1d1c56b528abe32d69bef2c9ce868e\transformed\jetified-bigo-ads-5.3.0\AndroidManifest.xml:83:13-71
999        <activity
999-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:21:9-25:52
1000            android:name="com.fyber.inneractive.sdk.activities.InneractiveInternalBrowserActivity"
1000-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:22:13-99
1001            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1001-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:23:13-106
1002            android:hardwareAccelerated="true"
1002-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:24:13-47
1003            android:screenOrientation="fullUser" />
1003-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:25:13-49
1004        <activity
1004-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:26:9-30:74
1005            android:name="com.fyber.inneractive.sdk.activities.InneractiveFullscreenAdActivity"
1005-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:27:13-96
1006            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1006-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:28:13-106
1007            android:hardwareAccelerated="true"
1007-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:29:13-47
1008            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
1008-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:30:13-71
1009        <activity
1009-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:31:9-35:74
1010            android:name="com.fyber.inneractive.sdk.activities.InneractiveRichMediaVideoPlayerActivityCore"
1010-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:32:13-108
1011            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1011-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:33:13-106
1012            android:hardwareAccelerated="true"
1012-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:34:13-47
1013            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
1013-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:35:13-71
1014        <activity
1014-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:36:9-41:75
1015            android:name="com.fyber.inneractive.sdk.activities.InternalStoreWebpageActivity"
1015-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:37:13-93
1016            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1016-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:38:13-106
1017            android:excludeFromRecents="true"
1017-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:39:13-46
1018            android:screenOrientation="sensor"
1018-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:40:13-47
1019            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1019-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:41:13-72
1020        <activity
1020-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:42:9-46:52
1021            android:name="com.fyber.inneractive.sdk.activities.FyberReportAdActivity"
1021-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:43:13-86
1022            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
1022-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:44:13-106
1023            android:hardwareAccelerated="true"
1023-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:45:13-47
1024            android:screenOrientation="fullUser" /> <!-- mbridge base activity -->
1024-->[com.fyber:marketplace-sdk:8.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78cdf045931564b43e4abbe8bc59614b\transformed\jetified-marketplace-sdk-8.3.7\AndroidManifest.xml:46:13-49
1025        <activity
1025-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:23:9-28:80
1026            android:name="com.mbridge.msdk.activity.MBCommonActivity"
1026-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:24:13-70
1027            android:configChanges="keyboard|orientation"
1027-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:25:13-57
1028            android:excludeFromRecents="true"
1028-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:26:13-46
1029            android:exported="false"
1029-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:27:13-37
1030            android:theme="@style/mbridge_transparent_common_activity_style" /> <!-- integration rewardVideo if aggregation nativeX pls add start -->
1030-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:28:13-77
1031        <activity android:name="com.mbridge.msdk.out.LoadingActivity" /> <!-- integration rewardVideo if aggregation nativeX pls add end -->
1031-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:9-73
1031-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:31:19-70
1032        <activity
1032-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:33:9-37:75
1033            android:name="com.mbridge.msdk.newreward.player.MBRewardVideoActivity"
1033-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:34:13-83
1034            android:configChanges="orientation|keyboardHidden|screenSize"
1034-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:35:13-74
1035            android:excludeFromRecents="true"
1035-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:36:13-46
1036            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1036-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:37:13-72
1037        <activity
1037-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:38:9-42:75
1038            android:name="com.mbridge.msdk.reward.player.MBRewardVideoActivity"
1038-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:39:13-80
1039            android:configChanges="orientation|keyboardHidden|screenSize"
1039-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:40:13-74
1040            android:excludeFromRecents="true"
1040-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:41:13-46
1041            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
1041-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:42:13-72
1042
1043        <receiver
1043-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:44:9-50:20
1044            android:name="com.mbridge.msdk.foundation.same.broadcast.NetWorkChangeReceiver"
1044-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:45:13-92
1045            android:exported="true" >
1045-->[com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.71] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25197d2b38fc8d21bd942743667033fe\transformed\jetified-mbridge_android_sdk-16.9.71\AndroidManifest.xml:46:13-36
1046            <intent-filter>
1046-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
1047                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
1047-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
1047-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c452d32d63b1427cc5b8d6e13c7a2d8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
1048            </intent-filter>
1049        </receiver>
1050    </application>
1051
1052</manifest>
