<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="DontKillMyApp">不要杀死我的应用</string>
    <string name="Dutch_lang">荷兰语</string>
    <string name="English_lang">英语</string>
    <string name="French_lang">法语</string>
    <string name="Germany_lang">德语</string>
    <string name="Hungarian_lang">匈牙利语</string>
    <string name="Italy_lang">意大利语</string>
    <string name="Poland_lang">波兰语</string>
    <string name="Portuguese_lang">葡萄牙语</string>
    <string name="Privacy_Policy">隐私政策</string>
    <string name="Romanian_lang">罗马尼亚语</string>
    <string name="Russian_lang">俄语</string>
    <string name="Spanish_lang">西班牙语</string>
    <string name="Turkish_lang">土耳其语</string>
    <string name="Ukraine_lang">乌克兰语</string>
    <string name="_hinese_traditional_lang_res_0x7f130305">中文（繁体）</string>
    <string name="abc_action_menu_overflow_description">更多选项</string>
    <string name="abc_action_mode_done">完成</string>
    <string name="abc_capital_off">关闭</string>
    <string name="abc_capital_on">开启</string>
    <string name="abc_search_hint">搜索…</string>
    <string name="abc_searchview_description_clear">清除查询</string>
    <string name="abc_searchview_description_search">搜索</string>
    <string name="abc_searchview_description_submit">提交查询</string>
    <string name="abc_searchview_description_voice">语音搜索</string>
    <string name="abc_toolbar_collapse_description">收起</string>
    <string name="about_translations">关于翻译</string>
    <string name="active_mode">屏幕常亮</string>
    <string name="ads_not_available">广告不可用</string>
    <string name="all">总计</string>
    <string name="all_info">此模块显示设备在当前电量下，根据您的使用方式可持续工作的时间。</string>
    <string name="all_time_charge">总时间</string>
    <string name="alternative_charge_level">备用电量检测方法</string>
    <string name="amoled">AMOLED</string>
    <string name="amoled_inverted">AMOLED反色</string>
    <string name="amperage_current_session">当前会话电流图</string>
    <string name="animation">动画</string>
    <string name="animation_info_desc">动画功能可显示自定义电池充电动画。设备充电时出现，点击即可关闭。您也可以在设置中禁用此功能。</string>
    <string name="anti_thief">防盗</string>
    <string name="anti_thief_info">防盗功能可在充电时保护您的设备。如果充电器被拔出且5秒内未输入正确密码，将播放响亮的警告音提醒您。您可以在应用设置中启用或禁用此功能。</string>
    <string name="app">应用程序</string>
    <string name="app_name">电池充电动画3D</string>
    <string name="applied">已应用</string>
    <string name="applovin_agree_message">如果您选择开始使用我们的应用，请点击\"继续\"</string>
    <string name="applovin_alt_privacy_policy_text"/>
    <string name="applovin_continue_button_text">继续</string>
    <string name="applovin_pp_and_tos_title">使用%s之前, 您必须同意我们的服务条款，并确认您已经阅读并知悉我们的隐私政策。</string>
    <string name="applovin_pp_title">使用%s之前, 您必须确认您已经阅读并知悉我们的隐私政策.</string>
    <string name="applovin_privacy_policy_text">隐私政策</string>
    <string name="applovin_terms_of_service_text">服务条款</string>
    <string name="applovin_terms_of_use_text">使用条款</string>
    <string name="apply">应用</string>
    <string name="apply_for_24hrs">应用24小时</string>
    <string name="arabic_lang">阿拉伯语</string>
    <string name="auto">自动</string>
    <string name="auto_stab_database">数据库自动稳定</string>
    <string name="average_speed">平均速度</string>
    <string name="average_speed_charge">充电平均速度</string>
    <string name="avg_charge_info">此模块显示所有会话的平均充电速度。您可以通过在设置中清除数据库来重置此数据。\n\n屏幕开启为屏幕亮时的平均速度。\n屏幕关闭为屏幕熄灭时的平均速度。\n组合使用为根据您充电时的使用方式计算的平均速度。</string>
    <string name="avg_discharge_info">此模块显示所有会话的平均放电速度。您可以通过在设置中清除数据库来重置此数据。\n\n屏幕开启为屏幕亮时的平均速度。\n屏幕关闭为屏幕熄灭时的平均速度。\n组合使用为根据您放电时的使用方式计算的平均速度。</string>
    <string name="awake">唤醒</string>
    <string name="background_permission_allow">允许</string>
    <string name="background_permission_close">关闭</string>
    <string name="background_permission_dialog_message">为保证正常工作并显示正确数据，BatteryApp必须始终在后台运行。如果BatteryApp未在后台运行，将无法执行电量警报等重要功能。\n\n不幸的是，在许多设备上，操作系统会停止收集必要电池数据的服务。Don\'t Kill My App是一个帮助用户为各种设备解决此问题的网站。\n\n对于Android 11及以上版本，授予持续后台运行权限变得至关重要。请授予此权限。</string>
    <string name="background_permission_dialog_title">重要信息！</string>
    <string name="background_permission_dont_kill_link">Don\'t Kill My App</string>
    <string name="battery_info">电池信息（Android）</string>
    <string name="battery_level_alert">电量警报</string>
    <string name="battery_notify_text_start">选择是否在电池达到所选百分比时接收通知。您可以随时关闭此功能。</string>
    <string name="battery_wear_info">每次为设备充电时，电池都会磨损。在此模块中，您可以大致了解将电池充到所选百分比时电池将受到多少损害。电池的磨损周期比越高，电池受到的损害越大。</string>
    <string name="battery_wear_info_up">什么是电池磨损？</string>
    <string name="bla">全屏警报</string>
    <string name="black">深色</string>
    <string name="black_inverted">深色反色</string>
    <string name="buy_advanced_access">获取高级访问</string>
    <string name="by_TJ">由TJ设计</string>
    <string name="calculated_capacity">计算容量</string>
    <string name="calculated_for">为%s个会话计算</string>
    <string name="calibration_going">等待诊断</string>
    <string name="cancel">取消</string>
    <string name="cannot_load_video">无法加载视频</string>
    <string name="change_design_capacity">更改设计容量</string>
    <string name="charge">充电</string>
    <string name="charge_alarm">充电完成通知</string>
    <string name="charge_alarm_text">启用此功能后，每次电池充到所选电量时，您都会收到通知。</string>
    <string name="charge_notification">充电通知</string>
    <string name="charge_notification_switch">设备充电时通知</string>
    <string name="charge_notification_text">您的设备正在充电</string>
    <string name="charge_reached">电池已充满！</string>
    <string name="charge_to">充到%1$s%%会导致%2$s次磨损周期</string>
    <string name="charging">%1$s%%的电量足够使用</string>
    <string name="charging_animation">充电动画</string>
    <string name="choose_color">选择颜色</string>
    <string name="choose_theme">选择主题</string>
    <string name="clear_database">清除数据库</string>
    <string name="complex_use">组合使用</string>
    <string name="confirm">确认</string>
    <string name="confirm_and_continue">接受并继续</string>
    <string name="confirmed">已接受隐私政策条款。谢谢！</string>
    <string name="cumulative">累计</string>
    <string name="cumulative_text">此方法更方便，因为不需要用户完全充电。但其准确性可能不如单次法。要更新统计数据，请至少充电15%。注意，会话数较少时，电池磨损水平可能与实际有较大差异。</string>
    <string name="current_charging">电流</string>
    <string name="current_session">当前会话</string>
    <string name="current_session_info">此模块显示当前或上一次充电会话的信息。如果电池正在充电，则显示当前会话。如果电池正在放电，则显示上一次至少充入1%的会话。您可以使用手动会话重置立即重置此数据。您可以在设置中启用此功能。\n\n平均速度为整体充电速度。\n屏幕开启为屏幕亮时的充电速度。\n屏幕关闭为屏幕熄灭时的充电速度。</string>
    <string name="current_session_info_discharge">此模块显示当前或上一次放电会话的信息。如果电池正在放电，则显示当前会话。如果电池正在充电，则显示上一次至少放出1%的会话。您可以使用手动会话重置立即重置此数据。您可以在设置中启用此功能。\n\n平均速度为整体放电速度。\n屏幕开启为屏幕亮时的放电速度。\n屏幕关闭为屏幕熄灭时的放电速度。</string>
    <string name="damage_of_battery_charge">电池磨损</string>
    <string name="data_base">数据库</string>
    <string name="day_info">此模块显示在当前电量下，屏幕常亮时设备可持续工作的时间。</string>
    <string name="dead_time">电池磨损预测</string>
    <string formatted="false" name="dead_time_text_down">\t为减少磨损速度并延长电池寿命，建议将电池电量保持在20%到80%之间。\n\t当电池磨损超过20%时，建议更换新电池。</string>
    <string name="deep_sleep">深度睡眠</string>
    <string name="degree_of_wear_info">此模块显示有关电池状态的近似信息。\n\n设计容量是制造商指定的原始电池容量。\n计算容量是本程序计算出的电池容量。\n\n注意。如果您的电池健康度低于50%，则可能是双电芯电池。要修正容量显示，需在设置中启用双电芯电池选项。</string>
    <string name="design_capacity">设计容量</string>
    <string name="design_capacity_text_dialog">设计容量是制造商声明的电池容量，单位为mAh。</string>
    <string name="design_capacity_text_error">无效值。请务必填写正确容量，因为它会影响部分功能。</string>
    <string name="design_capacity_text_incorrect">设计容量已自动检测。如果不正确，您可以更改。</string>
    <string name="desing_capacity">设计容量：</string>
    <string name="device">设备：</string>
    <string name="discharge">放电</string>
    <string name="discharge_notification">放电通知</string>
    <string name="discharge_notification_switch">设备放电时通知</string>
    <string name="discharge_notification_text">您的设备未在充电</string>
    <string name="discharging">放电：</string>
    <string name="disconnect_charge">请断开设备充电。仅在设备放电时进行诊断。</string>
    <string name="display_charging_amperage_graph">显示充电电流图</string>
    <string name="do_not_disturb">请勿打扰</string>
    <string name="do_not_disturb_from">请勿打扰起始时间</string>
    <string name="do_not_disturb_until">请勿打扰结束时间</string>
    <string name="dont_kill_my_app1">为保证正常工作并显示正确数据，BatteryApp必须始终在后台运行。如果BatteryApp未在后台运行，将无法执行电量警报等重要功能。</string>
    <string name="dont_kill_my_app2">不幸的是，在许多设备上，操作系统会停止收集必要电池数据的服务。Don\'t Kill My App是一个帮助用户为各种设备解决此问题的网站。</string>
    <string name="dont_kill_my_app3">对于Android 11及以上版本，授予持续后台运行权限变得至关重要。请授予此权限。</string>
    <string name="dont_kill_my_app4">请为BatteryApp启用自启动，以便服务被杀死时能自动重启。</string>
    <string name="dont_kill_my_app_permission">后台运行权限</string>
    <string name="dual_battery">双电芯电池设置</string>
    <string name="enable_notification">启用通知</string>
    <string name="enable_vibration">振动</string>
    <string name="enter_password">输入密码</string>
    <string name="error">错误</string>
    <string name="error_accessing_file">访问文件出错。请重试。</string>
    <string name="export_database">导出数据库</string>
    <string name="for_time_up_charge">000小时 000分</string>
    <string name="full_battery_info">此模块显示设备满电后可持续工作的近似时间。该信息基于您设备的历史使用数据。</string>
    <string name="full_time">全部时间</string>
    <string name="fullbattery_top">电池已完全充满！</string>
    <string name="grant_permission">授予权限</string>
    <string name="graph">电池磨损图表</string>
    <string name="graph_percentage_text_down">\t此图表显示您选择的时间段内电池百分比的变化。</string>
    <string name="graph_temp">温度图表</string>
    <string name="graph_temp_text_down">\t此图表显示您选择的时间段内电池温度的变化。</string>
    <string name="graph_text_down">\t此图表显示上周电池的磨损速率。每一列代表一天。列的高度表示电池在特定日期受到的磨损程度。</string>
    <string name="grey">灰色</string>
    <string name="grey_inverted">灰色反色</string>
    <string name="h_16">16小时</string>
    <string name="h_24">24小时</string>
    <string name="h_4">4小时</string>
    <string name="h_8">8小时</string>
    <string name="hand_reset_sessions">手动会话重置</string>
    <string name="health">健康</string>
    <string name="history">使用历史</string>
    <string name="icon">应用图标</string>
    <string name="import_database">导入数据库</string>
    <string name="important_information">重要信息！</string>
    <string name="incorrect_password">密码错误！</string>
    <string name="info_in_current_session">电量损失</string>
    <string name="info_text">显示提示</string>
    <string name="language">语言</string>
    <string name="light">浅色</string>
    <string name="light__nverted_res_0x7f130141">浅色反色</string>
    <string name="loss_charge_info">此模块显示当前或上一次会话期间消耗的电量百分比和mAh。如果电池正在放电，则显示当前会话。如果电池正在充电，则显示上一次至少放出1%的会话。\n\n屏幕开启为屏幕亮时的消耗量。\n屏幕关闭为屏幕熄灭时的消耗量。</string>
    <string name="low_battery">电量低</string>
    <string name="low_battery_alarm">低电量通知</string>
    <string name="low_battery_alarm_text">启用此功能后，每次电池电量降至所选水平时，您都会收到通知。</string>
    <string name="low_battery_text_1">电池电量已达到</string>
    <string name="low_battery_text_2">。请为您的设备充电。</string>
    <string name="m36">36分</string>
    <string name="m8">8分</string>
    <string name="mA">\u0020毫安</string>
    <string name="ma">\u0020毫安时 </string>
    <string name="ma_in_medium">\u0020毫安平均</string>
    <string name="maximum_capacity">电池健康度</string>
    <string name="mbridge_cm_feedback_btn_text">反馈</string>
    <string name="mbridge_cm_feedback_dialog_close_close">关闭</string>
    <string name="mbridge_cm_feedback_dialog_close_submit">提交</string>
    <string name="mbridge_cm_feedback_dialog_content_fraud">虚假诈欺</string>
    <string name="mbridge_cm_feedback_dialog_content_misleading">诱导点击</string>
    <string name="mbridge_cm_feedback_dialog_content_not_play">内容无法正常展示</string>
    <string name="mbridge_cm_feedback_dialog_content_other">其他问题</string>
    <string name="mbridge_cm_feedback_dialog_content_por_violence">黄色暴力</string>
    <string name="mbridge_cm_feedback_dialog_content_sound_problems">声音问题</string>
    <string name="mbridge_cm_feedback_dialog_privacy_des">Mintegral隐私政策</string>
    <string name="mbridge_cm_feedback_dialog_submit_notice">感谢您的反馈！</string>
    <string name="mbridge_cm_feedback_dialog_title">用户反馈</string>
    <string name="mbridge_reward_appdesc">app desc</string>
    <string name="mbridge_reward_apptitle">app title</string>
    <string name="mbridge_reward_clickable_cta_btntext">Play Game</string>
    <string name="mbridge_reward_endcard_ad">AD</string>
    <string name="mbridge_reward_endcard_vast_notice">Do you want to learn more?</string>
    <string name="mbridge_reward_heat_count_unit">万</string>
    <string name="mbridge_reward_install">Install Now</string>
    <string name="mbridge_reward_video_view_reward_time_complete">已获得奖励</string>
    <string name="mbridge_reward_video_view_reward_time_left">s后即可获得奖励</string>
    <string name="mbridge_reward_video_view_reward_time_left_skip_time">秒后可跳过</string>
    <string name="mbridge_reward_viewed_text_str">您浏览过的内容</string>
    <string name="mbridge_splash_count_time_can_skip">点击跳过|</string>
    <string name="mbridge_splash_count_time_can_skip_not">后自动关闭</string>
    <string name="mbridge_splash_count_time_can_skip_s">s</string>
    <string name="measurement_parameter">测量参数：</string>
    <string name="my_subscribe">我的订阅</string>
    <string name="needed_advanced_access">需要高级访问</string>
    <string name="night_info">此模块显示在当前电量下，屏幕关闭时设备可持续工作的时间。</string>
    <string name="no">否</string>
    <string name="no_video_url_provided">未提供视频URL</string>
    <string name="not_identified">未识别</string>
    <string name="notification">通知</string>
    <string name="notification_for_tempBat">电池过热时通知</string>
    <string name="notifications_permission_confirm">允许</string>
    <string name="notifications_permission_decline">不允许</string>
    <string name="notifications_permission_title">允许应用向您发送通知吗？</string>
    <string name="notify_access">要使用此功能，您必须授予显示通知的权限。</string>
    <string name="notify_full">充满电时通知</string>
    <string name="now">现在</string>
    <string name="operating_time">屏幕关闭时的运行时间</string>
    <string name="operating_time_info">在此模块中，您可以查看设备在屏幕关闭时的运行时间。\n\n唤醒为手机用于处理任务的总时长。\n深度睡眠为手机处理器空闲的总时长。</string>
    <string name="overlay_permission_denied">悬浮窗权限被拒绝，无法继续。</string>
    <string name="overlay_permission_message">我们需要权限以在其他应用上方显示动画，包括在充电时锁屏显示。</string>
    <string name="overlay_permission_title">需要悬浮窗权限</string>
    <string name="overlay_will_appear_when_charging">充电时将显示悬浮窗。</string>
    <string name="password">密码</string>
    <string name="percent">% </string>
    <string name="percent_in_hour">\u0020%/小时</string>
    <string name="percent_without_tab">%</string>
    <string name="percentage_graph">百分比图表</string>
    <string name="permission_granted">权限已授予。谢谢！</string>
    <string name="pers">个性化</string>
    <string name="play_warning_sound">播放警告音</string>
    <string name="please_disconnect_charger">请关闭充电。</string>
    <string name="polarity">极性：</string>
    <string name="power">功率</string>
    <string name="premium">高级版</string>
    <string name="privacy_policy_starting">请阅读并接受隐私政策以继续。</string>
    <string name="privacy_setting">隐私设置</string>
    <string name="projected_time_charge_to_100">预计充满电所需时间</string>
    <string name="projected_time_charge_to_var">预计充到%1$s%%所需时间</string>
    <string name="rate">评分和评论</string>
    <string name="real_capacity">实际电池容量</string>
    <string name="real_capacity_text">通过两种算法，BatteryApp可以计算电池的实际容量并预测其变为不可用所需的时间。</string>
    <string name="remaining_time_charge">剩余充电时间</string>
    <string name="remove_add">移除广告</string>
    <string name="reset_sessions">重置会话</string>
    <string name="saving_video">正在保存视频，请稍候...</string>
    <string name="screen_off">屏幕关闭</string>
    <string name="second_color">第二主题色</string>
    <string name="set_a_password_to_turn_off_the_anti_thief_alert_when_charging_is_disconnected">设置密码以在断开充电时关闭防盗警报。</string>
    <string name="setting_notify">通过Android设置通知</string>
    <string name="setting_notify_battery_service">在通知中显示信息</string>
    <string name="setting_notify_rate">通知更新频率</string>
    <string name="settings">设置</string>
    <string name="settings_autostart">自启动设置</string>
    <string name="show_date_and_time">显示日期和时间</string>
    <string name="show_on_lockscreen">在锁屏上显示</string>
    <string name="since">自%1$s起</string>
    <string name="since_above">从%1$s到%2$s</string>
    <string name="singular">单次</string>
    <string formatted="false" name="singular_no_data">请将电量从15%以下充到100%以显示数据。</string>
    <string formatted="false" name="singular_text">此方法可能更准确，因为它只考虑上一次完全充电。但对用户要求更高，需要从15%以下充到100%。为提高数据准确性，请勿在收到充满通知前停止充电。</string>
    <string name="stab_database">手动数据库稳定</string>
    <string name="state_empty">空</string>
    <string name="status_charge">充电状态</string>
    <string name="status_icon">状态栏通知图标</string>
    <string name="support_us">支持项目</string>
    <string name="temperature">温度</string>
    <string name="theme">主题</string>
    <string name="time_remaining">剩余时间</string>
    <string name="time_work_on_us">时间为我们工作！</string>
    <string name="time_work_on_us_text">BatteryApp会逐步收集数据，为您展示最准确的统计信息。很简单，您使用BatteryApp的时间越长，统计数据就越准确！</string>
    <string name="timework_on_fullbaterry">满电时间估算</string>
    <string name="tt_ad_close_text">广告关闭</string>
    <string name="tt_ad_data_error">返回数据缺少必要字段</string>
    <string name="tt_ad_is_closed">广告已关闭</string>
    <string name="tt_ad_logo_txt">AD</string>
    <string name="tt_add_bad_reason">添加你认为广告较差的原因</string>
    <string name="tt_adslot_empty">广告位不能为空</string>
    <string name="tt_adslot_id_error">广告位id不合法</string>
    <string name="tt_adslot_size_empty">广告位尺寸不能为空</string>
    <string name="tt_app_empty">请求app不能为空</string>
    <string name="tt_bad">较差</string>
    <string name="tt_banner_ad_load_image_error">banner广告加载图片失败</string>
    <string name="tt_choose_language">zh</string>
    <string name="tt_comment_num">%1$s个评分</string>
    <string name="tt_content_type">http conent_type错误</string>
    <string name="tt_count_down_view">跳过</string>
    <string name="tt_dislike_header_tv_back">返回</string>
    <string name="tt_dislike_header_tv_title">选择</string>
    <string name="tt_display_error">显示异常</string>
    <string name="tt_done">完成</string>
    <string name="tt_error_access_method_pass">媒体接入类型不合法</string>
    <string name="tt_error_ad_type">广告类型不合法</string>
    <string name="tt_error_adtype_differ">媒体配置adtype和请求不一致</string>
    <string name="tt_error_apk_sign_check_error">apk签名sha1值与媒体平台录入不一致</string>
    <string name="tt_error_code_adcount_error">广告数量错误</string>
    <string name="tt_error_code_click_event_error">click event处理错误</string>
    <string name="tt_error_image_size">图片尺寸错误</string>
    <string name="tt_error_media_id">媒体id不合法</string>
    <string name="tt_error_media_type">媒体类型不合法</string>
    <string name="tt_error_new_register_limit">开发注册新上线广告位超出日请求量限制</string>
    <string name="tt_error_origin_ad_error">媒体请求素材是否原生与媒体平台录入不一致</string>
    <string name="tt_error_package_name">媒体包名异常</string>
    <string name="tt_error_redirect">redirect参数不正确</string>
    <string name="tt_error_request_invalid">媒体整改超过期限，请求非法</string>
    <string name="tt_error_slot_id_app_id_differ">SlotId和AppId匹配异常</string>
    <string name="tt_error_splash_ad_type">开屏广告类型异常</string>
    <string name="tt_error_union_os_error">os字段填的不对</string>
    <string name="tt_error_union_sdk_too_old">sdk 版本过低不返回广告</string>
    <string name="tt_error_unknow">未知异常</string>
    <string name="tt_error_verify_reward">激励视频验证服务器异常或处理失败</string>
    <string name="tt_feedback_experience_text">我们将为您带来更优质的广告体验</string>
    <string name="tt_feedback_submit_text">您已提交过反馈，请勿反复提交。</string>
    <string name="tt_feedback_thank_text">感谢您的反馈！</string>
    <string name="tt_feel_hint">你觉得这个广告怎么样？</string>
    <string name="tt_frequent_call_erroe">广告请求频率过高</string>
    <string name="tt_full_screen_skip_tx">5s后可跳过</string>
    <string name="tt_good">较好</string>
    <string name="tt_insert_ad_load_image_error">插屏广告图片加载失败</string>
    <string name="tt_like_this_ad">喜欢这个广告吗？</string>
    <string name="tt_load_creative_icon_error">icon图标加载失败</string>
    <string name="tt_load_creative_icon_response_error">icon加载response错误</string>
    <string name="tt_logo_cn">广告</string>
    <string name="tt_logo_en">广告</string>
    <string name="tt_msgPlayable">试玩后才可领取奖励</string>
    <string name="tt_multiple_ad_indicator">广告 %1$d/%2$d</string>
    <string name="tt_negtiveBtnBtnText">放弃奖励</string>
    <string name="tt_negtive_txt">取消</string>
    <string name="tt_net_error">网络请求失败</string>
    <string name="tt_no_ad">没有广告</string>
    <string name="tt_no_ad_parse">解析数据没有ad</string>
    <string name="tt_no_network">"无网络，请稍后再试"</string>
    <string name="tt_not_bad">不错</string>
    <string name="tt_other_reason">其他原因</string>
    <string name="tt_parse_fail">解析失败</string>
    <string name="tt_postiveBtnText">继续观看</string>
    <string name="tt_postiveBtnTextPlayable">继续试玩</string>
    <string name="tt_postive_txt">确定</string>
    <string name="tt_privacy_title">隐私政策</string>
    <string name="tt_reder_ad_load_timeout">模板广告加载超时无返回</string>
    <string name="tt_render_diff_template_invalid">模板差量无效</string>
    <string name="tt_render_fail_meta_invalid">模板物料数据异常</string>
    <string name="tt_render_fail_template_parse_error">模板数据解析异常</string>
    <string name="tt_render_fail_timeout">渲染超时未回调</string>
    <string name="tt_render_fail_unknown">渲染未知报错</string>
    <string name="tt_render_main_template_invalid">主模板无效</string>
    <string name="tt_render_render_parse_error">渲染结果数据解析失败</string>
    <string name="tt_report_this_ad">举报这个广告，以帮助我们改进</string>
    <string name="tt_request_body_error">请求实体为空</string>
    <string name="tt_request_pb_error">http request pb错误</string>
    <string name="tt_reward_feedback">反馈</string>
    <string name="tt_reward_full_skip">广告在%1$ss后可跳过</string>
    <string name="tt_reward_msg">观看完整视频才能获得奖励</string>
    <string name="tt_reward_screen_skip_tx">跳过</string>
    <string name="tt_ror_code_show_event_error">show event处理错误</string>
    <string name="tt_select_reason">请选择原因</string>
    <string name="tt_skip_ad_time_text">%ds后可跳过</string>
    <string name="tt_slide_up_3d">向上滑动</string>
    <string name="tt_splash_ad_load_image_error">开屏广告图片加载失败</string>
    <string name="tt_splash_brush_mask_hint">跳转详情页或第三方应用</string>
    <string name="tt_splash_brush_mask_title">滑动或者点击擦亮</string>
    <string name="tt_splash_cache_expired_error">缓存过期</string>
    <string name="tt_splash_cache_parse_error">缓存解析失败</string>
    <string name="tt_splash_default_click_shake">摇一摇或点击了解更多</string>
    <string name="tt_splash_not_have_cache_error">缓存中没有开屏广告</string>
    <string name="tt_splash_rock_text">前往详情页或第三方应用</string>
    <string name="tt_splash_rock_top_text">摇一摇 或 点击图标</string>
    <string name="tt_splash_wriggle_text">前往详情页或第三方应用</string>
    <string name="tt_splash_wriggle_top_text">扭动手机</string>
    <string name="tt_splash_wriggle_top_text_style_17">扭动手机 或 点击图标</string>
    <string name="tt_suggestion_commit">提交</string>
    <string name="tt_sys_error">服务器错误</string>
    <string name="tt_template_load_fail">模板主引擎加载失败</string>
    <string name="tt_text_privacy_app_version">版本号：V</string>
    <string name="tt_text_privacy_development">开发者：</string>
    <string name="tt_try_now">立即体验</string>
    <string name="tt_video_bytesize">流量</string>
    <string name="tt_video_dial_phone">立即拨打</string>
    <string name="tt_video_download_apk">立即下载</string>
    <string name="tt_video_mobile_go_detail">立即查看</string>
    <string name="tt_video_retry_des_txt">加载失败，请重试</string>
    <string name="tt_video_without_wifi_tips">播放将消耗%.2fMB流量</string>
    <string name="tt_wap_empty">请求wap不能为空</string>
    <string name="tt_web_title_default">广告</string>
    <string name="unexpected_error">意外错误！</string>
    <string name="update_downloading">正在下载更新…</string>
    <string name="update_install">安装</string>
    <string name="using_baterry_middle">平均电池使用量</string>
    <string name="using_energy">应用耗电量</string>
    <string name="v">\u0020伏</string>
    <string name="versions">应用版本：</string>
    <string name="video_saved">视频已保存</string>
    <string name="view_temp">温度显示</string>
    <string name="voltage">电压</string>
    <string name="watt">\u0020瓦</string>
    <string name="wear_rate">磨损程度</string>
    <string name="what_time">这是什么时间？</string>
    <string name="work_in_background">后台运行</string>
    <string name="write_me">联系我们</string>
    <string name="yes">是</string>
    <string name="zero">0</string>
    <string name="zero_seconds">0秒</string>
</resources>