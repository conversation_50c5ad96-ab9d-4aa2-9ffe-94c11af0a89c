package com.tqhit.battery.one.manager.thumbnail

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages local file operations for preloaded thumbnails.
 * Handles thumbnail storage, naming, existence checks, and cleanup operations.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles thumbnail file operations
 * - Open/Closed: Extensible for different storage strategies
 * - Dependency Inversion: Depends on abstractions (Context)
 */
@Singleton
class ThumbnailFileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "ThumbnailFileManager"
        private const val THUMBNAIL_DIRECTORY = "preloaded_thumbnails"
        private const val MAX_FILENAME_LENGTH = 100
        
        // Supported image formats
        private val SUPPORTED_EXTENSIONS = setOf(".jpg", ".jpeg", ".png", ".webp", ".gif")
        
        // File age threshold for cleanup (7 days)
        private const val FILE_EXPIRY_THRESHOLD_MS = 7 * 24 * 60 * 60 * 1000L
    }
    
    /**
     * Gets the directory for storing preloaded thumbnails.
     * Creates the directory if it doesn't exist.
     */
    private fun getThumbnailDirectory(): File {
        val thumbnailDir = File(context.filesDir, THUMBNAIL_DIRECTORY)
        if (!thumbnailDir.exists()) {
            thumbnailDir.mkdirs()
            BatteryLogger.d(TAG, "Created thumbnail directory: ${thumbnailDir.absolutePath}")
        }
        return thumbnailDir
    }
    
    /**
     * Generates a unique filename for a thumbnail URL.
     * Uses MD5 hash of the URL to ensure uniqueness and avoid filesystem issues.
     */
    fun generateThumbnailFileName(thumbnailUrl: String): String {
        return try {
            val hash = MessageDigest.getInstance("MD5")
                .digest(thumbnailUrl.toByteArray())
                .joinToString("") { "%02x".format(it) }
            
            // Determine file extension from URL
            val extension = extractFileExtension(thumbnailUrl)
            
            // Truncate if too long and add extension
            val baseName = if (hash.length > MAX_FILENAME_LENGTH) {
                hash.substring(0, MAX_FILENAME_LENGTH)
            } else {
                hash
            }
            
            "$baseName$extension"
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error generating filename for $thumbnailUrl", e)
            // Fallback to timestamp-based naming
            "thumbnail_${System.currentTimeMillis()}.jpg"
        }
    }
    
    /**
     * Extracts file extension from thumbnail URL.
     * Defaults to .jpg if extension cannot be determined.
     */
    private fun extractFileExtension(thumbnailUrl: String): String {
        return try {
            val url = thumbnailUrl.lowercase()
            SUPPORTED_EXTENSIONS.find { url.contains(it) } ?: ".jpg"
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Could not determine extension for $thumbnailUrl, using .jpg")
            ".jpg"
        }
    }
    
    /**
     * Checks if a preloaded thumbnail exists for the given URL.
     * Returns PreloadedThumbnailItem if file exists, null otherwise.
     */
    suspend fun getPreloadedThumbnail(
        thumbnailUrl: String,
        categoryName: String = "",
        animationMediaUrl: String = ""
    ): PreloadedThumbnailItem? = withContext(Dispatchers.IO) {
        try {
            val fileName = generateThumbnailFileName(thumbnailUrl)
            val file = File(getThumbnailDirectory(), fileName)
            
            if (file.exists() && file.isFile && file.length() > 0) {
                val status = if (isFileExpired(file)) {
                    ThumbnailPreloadStatus.EXPIRED
                } else {
                    ThumbnailPreloadStatus.COMPLETED
                }
                
                BatteryLogger.d(TAG, "Found preloaded thumbnail: ${file.absolutePath}, status: $status")
                
                PreloadedThumbnailItem(
                    thumbnailUrl = thumbnailUrl,
                    localFilePath = file.absolutePath,
                    status = status,
                    downloadTimestamp = file.lastModified(),
                    fileSizeBytes = file.length(),
                    categoryName = categoryName,
                    animationMediaUrl = animationMediaUrl
                )
            } else {
                BatteryLogger.d(TAG, "No preloaded thumbnail found for: $thumbnailUrl")
                null
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking preloaded thumbnail for $thumbnailUrl", e)
            null
        }
    }
    
    /**
     * Creates a file reference for storing a preloaded thumbnail.
     * Does not create the actual file, just returns the File object.
     */
    fun createThumbnailFile(thumbnailUrl: String): File {
        val fileName = generateThumbnailFileName(thumbnailUrl)
        return File(getThumbnailDirectory(), fileName)
    }
    
    /**
     * Validates that a thumbnail file was downloaded successfully.
     * Checks file existence, size, and basic image format validation.
     */
    suspend fun validateDownloadedThumbnail(file: File): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!file.exists() || !file.isFile) {
                BatteryLogger.w(TAG, "Thumbnail file does not exist or is not a file: ${file.absolutePath}")
                return@withContext false
            }
            
            if (file.length() == 0L) {
                BatteryLogger.w(TAG, "Thumbnail file is empty: ${file.absolutePath}")
                return@withContext false
            }
            
            // Basic image file validation - check for common image signatures
            file.inputStream().use { inputStream ->
                val buffer = ByteArray(10)
                val bytesRead = inputStream.read(buffer)
                
                if (bytesRead >= 4) {
                    val isValidImage = when {
                        // JPEG signature
                        buffer[0] == 0xFF.toByte() && buffer[1] == 0xD8.toByte() -> true
                        // PNG signature
                        buffer[0] == 0x89.toByte() && buffer[1] == 0x50.toByte() &&
                        buffer[2] == 0x4E.toByte() && buffer[3] == 0x47.toByte() -> true
                        // WebP signature
                        buffer[0] == 0x52.toByte() && buffer[1] == 0x49.toByte() &&
                        buffer[2] == 0x46.toByte() && buffer[3] == 0x46.toByte() -> true
                        // GIF signature (GIF87a or GIF89a)
                        buffer[0] == 0x47.toByte() && buffer[1] == 0x49.toByte() &&
                        buffer[2] == 0x46.toByte() && buffer[3] == 0x38.toByte() -> true
                        else -> false
                    }
                    
                    if (!isValidImage) {
                        BatteryLogger.w(TAG, "File does not appear to be a valid image: ${file.absolutePath}")
                        return@withContext false
                    }
                }
            }
            
            BatteryLogger.d(TAG, "Thumbnail validation successful: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error validating thumbnail file: ${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * Checks if a file is expired based on the expiry threshold.
     */
    private fun isFileExpired(file: File): Boolean {
        val fileAge = System.currentTimeMillis() - file.lastModified()
        return fileAge > FILE_EXPIRY_THRESHOLD_MS
    }
    
    /**
     * Cleans up old, invalid, or expired thumbnail files.
     * Returns the number of files cleaned up.
     */
    suspend fun cleanupThumbnailFiles(): Int = withContext(Dispatchers.IO) {
        var cleanedCount = 0
        
        try {
            val thumbnailDir = getThumbnailDirectory()
            val files = thumbnailDir.listFiles() ?: return@withContext 0
            
            for (file in files) {
                var shouldDelete = false
                var reason = ""
                
                if (!file.isFile) {
                    shouldDelete = true
                    reason = "not a file"
                } else if (file.length() == 0L) {
                    shouldDelete = true
                    reason = "empty file"
                } else if (isFileExpired(file)) {
                    shouldDelete = true
                    reason = "expired"
                } else if (!validateDownloadedThumbnail(file)) {
                    shouldDelete = true
                    reason = "failed validation"
                }
                
                if (shouldDelete) {
                    if (file.delete()) {
                        cleanedCount++
                        BatteryLogger.d(TAG, "Cleaned up thumbnail file ($reason): ${file.name}")
                    } else {
                        BatteryLogger.w(TAG, "Failed to delete thumbnail file: ${file.absolutePath}")
                    }
                }
            }
            
            BatteryLogger.d(TAG, "Thumbnail cleanup completed. Cleaned $cleanedCount files")
            cleanedCount
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during thumbnail cleanup", e)
            cleanedCount
        }
    }
    
    /**
     * Gets the total size of all preloaded thumbnails in bytes.
     */
    suspend fun getTotalThumbnailSize(): Long = withContext(Dispatchers.IO) {
        try {
            val thumbnailDir = getThumbnailDirectory()
            val files = thumbnailDir.listFiles() ?: return@withContext 0L
            
            files.filter { it.isFile }.sumOf { it.length() }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error calculating total thumbnail size", e)
            0L
        }
    }
    
    /**
     * Gets the count of preloaded thumbnail files.
     */
    suspend fun getPreloadedThumbnailCount(): Int = withContext(Dispatchers.IO) {
        try {
            val thumbnailDir = getThumbnailDirectory()
            val files = thumbnailDir.listFiles() ?: return@withContext 0
            
            files.count { it.isFile && it.length() > 0 }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error counting preloaded thumbnails", e)
            0
        }
    }
}
