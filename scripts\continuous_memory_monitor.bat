@echo off
REM TJ_BatteryOne Continuous Memory Monitoring Script
REM Provides real-time memory monitoring and performance tracking
REM Usage: continuous_memory_monitor.bat [device_id] [duration_minutes]

setlocal enabledelayedexpansion

REM Configuration
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set DEVICE_ID=%1
set DURATION_MINUTES=%2
if "%DEVICE_ID%"=="" set DEVICE_ID=emulator-5554
if "%DURATION_MINUTES%"=="" set DURATION_MINUTES=10

REM Monitoring configuration
set SAMPLE_INTERVAL=10
set ALERT_MEMORY_THRESHOLD=100000
set ALERT_MEMORY_INCREASE_THRESHOLD=20000

echo ========================================
echo TJ_BatteryOne Continuous Memory Monitor
echo ========================================
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo Duration: %DURATION_MINUTES% minutes
echo Sample Interval: %SAMPLE_INTERVAL% seconds
echo ========================================

REM Check ADB connection
echo [INFO] Checking ADB connection...
%ADB_PATH% -s %DEVICE_ID% shell echo "Device connected" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot connect to device %DEVICE_ID%
    exit /b 1
)
echo [SUCCESS] Device connected

REM Check if app is running
echo [INFO] Checking if app is running...
%ADB_PATH% -s %DEVICE_ID% shell ps | findstr %APP_ID% >nul
if errorlevel 1 (
    echo [WARNING] App is not currently running
    echo [INFO] Starting app...
    %ADB_PATH% -s %DEVICE_ID% shell am start -n %APP_ID%/com.tqhit.battery.one.activity.starting.StartingActivity >nul
    timeout /t 5 >nul
)
echo [SUCCESS] App is running

REM Create monitoring session directory
set SESSION_DIR=memory_monitor_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set SESSION_DIR=%SESSION_DIR: =0%
mkdir "%SESSION_DIR%" 2>nul

echo [INFO] Monitoring session: %SESSION_DIR%

REM Start background logcat monitoring
echo [INFO] Starting logcat monitoring...
start /b %ADB_PATH% -s %DEVICE_ID% logcat -s MEMORY_ANALYSIS:D MEMORY_USAGE:D PERFORMANCE_BENCHMARK:D GLIDE_MEMORY:D STORAGE_ANALYSIS:D > "%SESSION_DIR%\realtime_logcat.log"

REM Initialize monitoring variables
set /a TOTAL_SAMPLES=%DURATION_MINUTES% * 60 / %SAMPLE_INTERVAL%
set SAMPLE_COUNT=0
set BASELINE_MEMORY=0
set PEAK_MEMORY=0
set ALERT_COUNT=0

echo [INFO] Starting continuous monitoring for %TOTAL_SAMPLES% samples...
echo [INFO] Press Ctrl+C to stop monitoring early

REM Create CSV header
echo Timestamp,PSS_Total_KB,PSS_Java_KB,PSS_Native_KB,PSS_Graphics_KB,Private_Dirty_KB,Private_Clean_KB,Heap_Size_KB,Heap_Alloc_KB,Heap_Free_KB > "%SESSION_DIR%\memory_data.csv"

:monitoring_loop
if %SAMPLE_COUNT% GEQ %TOTAL_SAMPLES% goto :monitoring_complete

set /a SAMPLE_COUNT+=1
set CURRENT_TIME=%time%

echo.
echo [SAMPLE %SAMPLE_COUNT%/%TOTAL_SAMPLES%] %CURRENT_TIME%
echo ================================================

REM Get detailed memory information
%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% > "%SESSION_DIR%\temp_meminfo.txt"

REM Extract memory values
for /f "tokens=1,2,3,4,5,6" %%a in ('findstr "TOTAL" "%SESSION_DIR%\temp_meminfo.txt"') do (
    set PSS_TOTAL=%%b
    set PRIVATE_DIRTY=%%c
    set PRIVATE_CLEAN=%%d
    goto :total_found
)
:total_found

for /f "tokens=1,2,3,4,5,6" %%a in ('findstr "Java Heap:" "%SESSION_DIR%\temp_meminfo.txt"') do (
    set PSS_JAVA=%%c
    goto :java_found
)
:java_found

for /f "tokens=1,2,3,4,5,6" %%a in ('findstr "Native Heap:" "%SESSION_DIR%\temp_meminfo.txt"') do (
    set PSS_NATIVE=%%c
    goto :native_found
)
:native_found

for /f "tokens=1,2,3,4,5,6" %%a in ('findstr "Graphics:" "%SESSION_DIR%\temp_meminfo.txt"') do (
    set PSS_GRAPHICS=%%c
    goto :graphics_found
)
:graphics_found

REM Get heap information
%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% | findstr "Heap" > "%SESSION_DIR%\temp_heap.txt"
for /f "tokens=2,4,6" %%a in ('findstr "Heap:" "%SESSION_DIR%\temp_heap.txt"') do (
    set HEAP_SIZE=%%a
    set HEAP_ALLOC=%%b
    set HEAP_FREE=%%c
    goto :heap_found
)
:heap_found

REM Set baseline on first sample
if %SAMPLE_COUNT%==1 set BASELINE_MEMORY=%PSS_TOTAL%

REM Update peak memory
if %PSS_TOTAL% GTR %PEAK_MEMORY% set PEAK_MEMORY=%PSS_TOTAL%

REM Calculate memory increase from baseline
set /a MEMORY_INCREASE=%PSS_TOTAL% - %BASELINE_MEMORY%

REM Display current metrics
echo PSS Total: %PSS_TOTAL% KB
echo PSS Java: %PSS_JAVA% KB
echo PSS Native: %PSS_NATIVE% KB
echo PSS Graphics: %PSS_GRAPHICS% KB
echo Private Dirty: %PRIVATE_DIRTY% KB
echo Private Clean: %PRIVATE_CLEAN% KB
echo Heap Size: %HEAP_SIZE% KB
echo Heap Allocated: %HEAP_ALLOC% KB
echo Heap Free: %HEAP_FREE% KB
echo Memory Increase: %MEMORY_INCREASE% KB

REM Check for memory alerts
if %PSS_TOTAL% GTR %ALERT_MEMORY_THRESHOLD% (
    echo [ALERT] High memory usage: %PSS_TOTAL% KB ^> %ALERT_MEMORY_THRESHOLD% KB
    set /a ALERT_COUNT+=1
)

if %MEMORY_INCREASE% GTR %ALERT_MEMORY_INCREASE_THRESHOLD% (
    echo [ALERT] Significant memory increase: %MEMORY_INCREASE% KB from baseline
    set /a ALERT_COUNT+=1
)

REM Log to CSV
echo %CURRENT_TIME%,%PSS_TOTAL%,%PSS_JAVA%,%PSS_NATIVE%,%PSS_GRAPHICS%,%PRIVATE_DIRTY%,%PRIVATE_CLEAN%,%HEAP_SIZE%,%HEAP_ALLOC%,%HEAP_FREE% >> "%SESSION_DIR%\memory_data.csv"

REM Check for performance violations in logcat
%ADB_PATH% -s %DEVICE_ID% logcat -d -s PERFORMANCE_BENCHMARK:W | findstr "VIOLATION" > "%SESSION_DIR%\temp_violations.txt" 2>nul
for /f %%a in ('type "%SESSION_DIR%\temp_violations.txt" 2^>nul ^| find /c "VIOLATION"') do (
    if %%a GTR 0 echo [WARNING] %%a performance violations detected in logcat
)

REM Wait for next sample
timeout /t %SAMPLE_INTERVAL% >nul
goto :monitoring_loop

:monitoring_complete

echo.
echo ========================================
echo Monitoring Complete
echo ========================================

REM Stop logcat monitoring
taskkill /f /im adb.exe >nul 2>&1

REM Generate final report
echo [INFO] Generating monitoring report...

(
echo TJ_BatteryOne Continuous Memory Monitoring Report
echo Generated: %date% %time%
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo Duration: %DURATION_MINUTES% minutes
echo Samples: %SAMPLE_COUNT%
echo Sample Interval: %SAMPLE_INTERVAL% seconds
echo.
echo MEMORY STATISTICS:
echo - Baseline Memory: %BASELINE_MEMORY% KB
echo - Peak Memory: %PEAK_MEMORY% KB
echo - Final Memory: %PSS_TOTAL% KB
echo - Total Increase: %MEMORY_INCREASE% KB
echo - Alert Count: %ALERT_COUNT%
echo.
echo THRESHOLDS:
echo - Memory Alert: %ALERT_MEMORY_THRESHOLD% KB
echo - Increase Alert: %ALERT_MEMORY_INCREASE_THRESHOLD% KB
echo.
echo FILES GENERATED:
echo - memory_data.csv: Detailed memory metrics over time
echo - realtime_logcat.log: Complete logcat output
echo - temp_meminfo.txt: Latest memory info dump
echo - temp_heap.txt: Latest heap information
echo - temp_violations.txt: Performance violations
echo.
echo ANALYSIS RECOMMENDATIONS:
if %MEMORY_INCREASE% GTR %ALERT_MEMORY_INCREASE_THRESHOLD% echo - Investigate memory leak - increase of %MEMORY_INCREASE% KB
if %PEAK_MEMORY% GTR %ALERT_MEMORY_THRESHOLD% echo - Peak memory usage is high - %PEAK_MEMORY% KB
if %ALERT_COUNT% GTR 0 echo - %ALERT_COUNT% alerts triggered during monitoring
echo - Import memory_data.csv into Excel/Google Sheets for detailed analysis
echo - Review realtime_logcat.log for performance insights
echo - Monitor heap allocation patterns for optimization opportunities
) > "%SESSION_DIR%\monitoring_report.txt"

echo [SUCCESS] Monitoring completed successfully!
echo [INFO] Results saved to: %SESSION_DIR%
echo [INFO] Review monitoring_report.txt for summary
echo [INFO] Import memory_data.csv for detailed analysis

REM Display quick summary
echo.
echo QUICK SUMMARY:
echo ==============
echo Baseline: %BASELINE_MEMORY% KB
echo Peak: %PEAK_MEMORY% KB
echo Final: %PSS_TOTAL% KB
echo Increase: %MEMORY_INCREASE% KB
echo Alerts: %ALERT_COUNT%

REM Open results folder
start "" "%SESSION_DIR%"

REM Cleanup temp files
del "%SESSION_DIR%\temp_*.txt" 2>nul

endlocal
pause
