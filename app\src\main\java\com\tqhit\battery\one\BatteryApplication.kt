package com.tqhit.battery.one

import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import androidx.appcompat.app.AppCompatDelegate
import com.applovin.sdk.AppLovinSdk
import com.ironsource.mediationsdk.IronSource
import com.tqhit.adlib.sdk.AdLibHiltApplication
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.adlib.sdk.utils.Constant
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper
import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import com.tqhit.battery.one.service.animation.AnimationDataService
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.ForegroundServiceUtils
import com.tqhit.battery.one.utils.PreloadingMonitor
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject
import androidx.core.net.toUri
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinPrivacySettings
import com.applovin.sdk.AppLovinSdkConfiguration
import com.applovin.sdk.AppLovinSdkConfiguration.ConsentFlowUserGeography
import com.applovin.sdk.AppLovinSdkInitializationConfiguration
import com.facebook.ads.AdSettings
import com.google.firebase.FirebaseApp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner



@HiltAndroidApp
class BatteryApplication : AdLibHiltApplication(), LifecycleObserver {

    // Application-level coroutine scope for async initialization
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    @Inject
    lateinit var preferencesHelper: PreferencesHelper
    @Inject
    lateinit var appRepository: AppRepository
    @Inject
    lateinit var applovinNativeAdManager: com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
    @Inject
    lateinit var applovinRewardedAdManager: com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
    @Inject
    lateinit var applovinInterstitialAdManager: com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
    @Inject
    lateinit var applovinBannerAdManager: com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
    @Inject
    lateinit var applovinAppOpenAdManager: com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager
    // Legacy batteryStatusServiceHelper injection removed - service deprecated
    @Inject
    lateinit var coreBatteryServiceHelper: CoreBatteryServiceHelper
    @Inject
    lateinit var chargingOverlayServiceHelper: ChargingOverlayServiceHelper
    @Inject
    lateinit var animationPreloadingRepository: AnimationPreloadingRepository
    @Inject
    lateinit var animationDataService: AnimationDataService
    @Inject
    lateinit var preloadingMonitor: PreloadingMonitor
    @Inject
    lateinit var deferredThumbnailPreloadingService: com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService
    @Inject
    lateinit var firebaseInitMonitor: FirebaseInitializationMonitor
    @Inject
    lateinit var memoryAnalyzer: com.tqhit.battery.one.utils.MemoryAnalyzer
    @Inject
    lateinit var performanceProfiler: com.tqhit.battery.one.utils.PerformanceProfiler

    companion object {
        private const val TAG = "BatteryApplication"
        var appSession: Int = 0
        var appOpenTime: Long = System.currentTimeMillis()
    }

    override fun onCreate() {
        val startTime = System.currentTimeMillis()
        BatteryLogger.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() started at $startTime")

        // PERFORMANCE_OPTIMIZATION: Defer Firebase initialization to background
        // FirebaseApp.initializeApp(this) - moved to async initialization

        // Essential synchronous initialization only
        val superStartTime = System.currentTimeMillis()
        super.onCreate()
        BatteryLogger.logTiming(TAG, "super.onCreate()", System.currentTimeMillis() - superStartTime)

        // PERFORMANCE_OPTIMIZATION: Defer lifecycle observer to after critical path
        // ProcessLifecycleOwner.get().lifecycle.addObserver(this) - moved to async

        // Critical session tracking (keep synchronous for immediate availability)
        val prefsStartTime = System.currentTimeMillis()
        appSession = preferencesHelper.getInt("session", 0)
        appSession++
        preferencesHelper.saveInt("session", appSession)
        BatteryLogger.logTiming(TAG, "Preferences operations", System.currentTimeMillis() - prefsStartTime)

        // PERFORMANCE_OPTIMIZATION: Defer WebView setup to background thread
        // WebView setup moved to async initialization

        // Move heavy initialization to background thread immediately
        initializeAsyncComponents()

        BatteryLogger.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")

        // PERFORMANCE_OPTIMIZATION: Move AppLovin SDK initialization to async
        // AppLovin SDK setup moved to initializeAsyncComponents()
    }

    /**
     * Initialize heavy components asynchronously to avoid blocking app startup
     */
    private fun initializeAsyncComponents() {
        applicationScope.launch(Dispatchers.IO) {
            val asyncStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Async initialization started")

            try {
                // PERFORMANCE_OPTIMIZATION: Initialize Firebase in background
                val firebaseStartTime = System.currentTimeMillis()
                FirebaseApp.initializeApp(this@BatteryApplication)
                Log.d(TAG, "STARTUP_TIMING: Firebase initialization took ${System.currentTimeMillis() - firebaseStartTime}ms")

                // PERFORMANCE_OPTIMIZATION: Initialize lifecycle observer in background
                val lifecycleStartTime = System.currentTimeMillis()
                withContext(Dispatchers.Main) {
                    ProcessLifecycleOwner.get().lifecycle.addObserver(this@BatteryApplication)
                }
                Log.d(TAG, "STARTUP_TIMING: Lifecycle observer setup took ${System.currentTimeMillis() - lifecycleStartTime}ms")

                // PERFORMANCE_OPTIMIZATION: Initialize WebView in background
                val webViewStartTime = System.currentTimeMillis()
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                        val process = getProcessName()
                        if (packageName != process) WebView.setDataDirectorySuffix(process)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in WebView setup: ${e.message}")
                }
                Log.d(TAG, "STARTUP_TIMING: WebView setup took ${System.currentTimeMillis() - webViewStartTime}ms")

                // PERFORMANCE_OPTIMIZATION: Defer AppLovin SDK initialization until after UI is ready
                // This prevents blocking MainActivity startup
                Log.d(TAG, "STARTUP_TIMING: AppLovin SDK initialization deferred until UI ready")

                // Battery services startup (can be deferred)
                val servicesStartTime = System.currentTimeMillis()
                startBatteryStatusService()
                startCoreBatteryStatsService()
                startChargingOverlayService()
                startMemoryOptimizationService()
                Log.d(TAG, "STARTUP_TIMING: Async battery services startup took ${System.currentTimeMillis() - servicesStartTime}ms")

                // Animation preloading startup (can be deferred)
                val preloadStartTime = System.currentTimeMillis()
                startAnimationPreloading()
                Log.d(TAG, "STARTUP_TIMING: Animation preloading startup took ${System.currentTimeMillis() - preloadStartTime}ms")

                // Start Firebase initialization monitoring
                val firebaseMonitorStartTime = System.currentTimeMillis()
                firebaseInitMonitor.startMonitoring()
                Log.d(TAG, "STARTUP_TIMING: Firebase initialization monitoring started in ${System.currentTimeMillis() - firebaseMonitorStartTime}ms")

                // Initiate deferred thumbnail preloading (enhanced with Firebase monitoring)
                val thumbnailPreloadStartTime = System.currentTimeMillis()
                deferredThumbnailPreloadingService.initiateDeferredPreloading()
                Log.d(TAG, "STARTUP_TIMING: Enhanced deferred thumbnail preloading initiated in ${System.currentTimeMillis() - thumbnailPreloadStartTime}ms")

                Log.d(TAG, "STARTUP_TIMING: Total async initialization took ${System.currentTimeMillis() - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in async initialization", e)
            }
        }
    }

    /**
     * PERFORMANCE_OPTIMIZATION: Initialize AppLovin SDK after UI is ready
     * This prevents blocking the critical startup path
     */
    fun initializeAppLovinSDKDeferred() {
        applicationScope.launch(Dispatchers.IO) {
            val appLovinStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Starting deferred AppLovin SDK initialization")
            initializeAppLovinSDK()
            Log.d(TAG, "STARTUP_TIMING: Deferred AppLovin SDK initialization took ${System.currentTimeMillis() - appLovinStartTime}ms")
        }
    }

    /**
     * PERFORMANCE_OPTIMIZATION: Initialize AppLovin SDK asynchronously
     */
    private suspend fun initializeAppLovinSDK() {
        try {
            withContext(Dispatchers.Main) {
                val settings = AppLovinSdk.getInstance(this@BatteryApplication).settings
                settings.termsAndPrivacyPolicyFlowSettings.isEnabled = true
                settings.termsAndPrivacyPolicyFlowSettings.privacyPolicyUri =
                    "https://ahugames.com/privacy-policy.html".toUri()

                // Terms of Service URL is optional
                settings.termsAndPrivacyPolicyFlowSettings.termsOfServiceUri = null
                // Showing Terms & Privacy Policy flow in GDPR region is optional (disabled by default)
                settings.termsAndPrivacyPolicyFlowSettings.setShowTermsAndPrivacyPolicyAlertInGdpr(true)
                settings.setExtraParameter("google_test_device_hashed_id", "B3EEABB8EE11C2BE770B684D95219ECB")

                // Set the mediation provider value to "max" to ensure proper functionality
                val initConfig = AppLovinSdkInitializationConfiguration.builder("EWVXRT6zy56MNa68ZDiwGnha4SvOOS_z1yZIImOudCJzcu3twVTMBF9TorbmwU2w5pD9qiqO0rBIDPtvmrsRof")
                    .setMediationProvider(AppLovinMediationProvider.MAX)
                    .build()

                // Initialize the SDK with the configuration
                AppLovinSdk.getInstance(this@BatteryApplication).initialize(initConfig) { sdkConfig ->
                    appRepository.setConsentFlowUserGeography(sdkConfig.consentFlowUserGeography == ConsentFlowUserGeography.GDPR)
                    val hasMetaConsent = AppLovinPrivacySettings.getAdditionalConsentStatus(89)
                    if (hasMetaConsent != null) {
                        // Set Meta Data Processing Options accordingly.
                        AdSettings.setDataProcessingOptions(arrayOf<String>("LDU"), 1, 1000)
                    }

                    initTracker("")

                    val aoaEnable = remoteConfigHelper.getBoolean("aoa_enable")
                            && (appSession > 1 || remoteConfigHelper.getBoolean("aoa_enable_first_session"))
                    applovinAppOpenAdManager.setEnabled(aoaEnable)
                    applovinAppOpenAdManager.loadAppOpenAd()

                    val ivEnable = remoteConfigHelper.getBoolean("iv_enable")
                    if (ivEnable) {
                        val handler = Handler(Looper.getMainLooper())
                        val ivLoadDelay = if (appSession <= 1) remoteConfigHelper.getLong("iv_first_session_load_delay").toInt()
                        else remoteConfigHelper.getLong("iv_load_delay").toInt()
                        handler.postDelayed({
                            applovinInterstitialAdManager.loadInterstitialAd()
                        }, ivLoadDelay * 1000L)
                    }

                    val rvEnable = remoteConfigHelper.getBoolean("rv_enable")
                    if (rvEnable) {
                        val rvLoadInView = remoteConfigHelper.getBoolean("rv_load_in_view")
                        val rvLoadDelay = remoteConfigHelper.getLong("rv_load_delay").toInt()
                        if (!rvLoadInView) {
                            val handler = Handler(Looper.getMainLooper())
                            handler.postDelayed({
                                applovinRewardedAdManager.loadRewardedAd()
                            }, rvLoadDelay * 1000L)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing AppLovin SDK", e)
        }
    }

    override fun onCreateExt() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: onCreateExt() started at $startTime")

        // Essential synchronous setup
        Constant.DEBUG_MODE = false
        admobHelper.setAppOpenAdUnitId("")

        // PERFORMANCE_OPTIMIZATION: Optimized theme initialization
        val themeStartTime = System.currentTimeMillis()
        ThemeManager.initialize(this)

        // Cache theme selection to avoid repeated calls
        val selectedTheme = ThemeManager.getSelectedTheme()
        val nightMode = when (selectedTheme) {
            "AutoTheme" -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
            "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> AppCompatDelegate.MODE_NIGHT_YES
            else -> AppCompatDelegate.MODE_NIGHT_NO
        }
        AppCompatDelegate.setDefaultNightMode(nightMode)
        Log.d(TAG, "STARTUP_TIMING: Theme initialization took ${System.currentTimeMillis() - themeStartTime}ms")

        // Initialize language (needed for immediate UI)
        val languageStartTime = System.currentTimeMillis()
        initializeLanguage()
        Log.d(TAG, "STARTUP_TIMING: Language initialization took ${System.currentTimeMillis() - languageStartTime}ms")

        // Move non-critical initialization to background
        initializeNonCriticalComponents()

        Log.d(TAG, "STARTUP_TIMING: onCreateExt() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Initialize non-critical components asynchronously
     */
    private fun initializeNonCriticalComponents() {
        applicationScope.launch(Dispatchers.IO) {
            try {
                val remoteConfigStartTime = System.currentTimeMillis()
                initRemoteConfig(R.xml.remote_config_defaults)
                Log.d(TAG, "STARTUP_TIMING: Async remote config initialization took ${System.currentTimeMillis() - remoteConfigStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in non-critical initialization", e)
            }
        }
    }

    private fun initializeLanguage() {
        val savedLanguage = appRepository.getLanguage()
        val languageToUse = if (savedLanguage.isNotEmpty()) savedLanguage else appRepository.getDefaultLanguage()
        appRepository.setLocale(this, languageToUse)
    }

    override fun onActivityPreCreated(activity: Activity, savedInstanceState: Bundle?) {
        super.onActivityPreCreated(activity, savedInstanceState)
        ThemeManager.applyTheme(activity)
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
        IronSource.onResume(activity)
    }

    override fun onActivityPaused(activity: Activity) {
        super.onActivityPaused(activity)
        IronSource.onPause(activity)
    }

    /**
     * DEPRECATED: Legacy BatteryStatusService - kept for reference only
     * This service has been replaced by CoreBatteryStatsService for unified battery monitoring
     * Multiple battery services were causing resource waste and data inconsistency
     */
    private fun startBatteryStatusService() {
        Log.d(TAG, "DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead")
        // Legacy service startup commented out to eliminate duplicate battery monitoring
        // batteryStatusServiceHelper.startService()
    }

    /**
     * Starts the CoreBatteryStatsService to provide core battery monitoring.
     * Includes comprehensive permission checking and error handling.
     */
    private fun startCoreBatteryStatsService() {
        Log.d(TAG, "Starting CoreBatteryStatsService from Application")

        // Log current permission status for debugging
        ForegroundServiceUtils.logPermissionStatus(this)

        try {
            // Check if we have required permissions
            if (!ForegroundServiceUtils.hasRequiredPermissions(this)) {
                Log.w(TAG, "Missing required permissions for foreground service, starting in fallback mode")
            }

            coreBatteryServiceHelper.startService()

            // Log service status after startup attempt
            val serviceStatus = coreBatteryServiceHelper.getServiceStatus()
            Log.d(TAG, "CoreBatteryStatsService startup status: $serviceStatus")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start CoreBatteryStatsService", e)

            // Log additional context for debugging
            val permissionStatus = ForegroundServiceUtils.getPermissionStatus(this)
            Log.e(TAG, "Permission status during failure: $permissionStatus")
        }
    }

    /**
     * Starts the ChargingOverlayService for charging animation display
     * Only starts if configuration requirements are met (overlay enabled, trial valid, etc.)
     */
    private fun startChargingOverlayService() {
        BatteryLogger.d(TAG, "Starting ChargingOverlayService from Application")
        try {
            chargingOverlayServiceHelper.startServiceIfNeeded()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start ChargingOverlayService", e)
        }
    }

    /**
     * Starts MemoryOptimizationService for comprehensive memory and performance monitoring.
     * Provides real-time memory optimization, storage quota enforcement, and performance profiling.
     */
    private fun startMemoryOptimizationService() {
        BatteryLogger.d(TAG, "MEMORY_OPTIMIZATION: Starting MemoryOptimizationService from Application")
        try {
            val intent = android.content.Intent(this, com.tqhit.battery.one.service.optimization.MemoryOptimizationService::class.java)
            startService(intent)

            // Start performance profiling
            performanceProfiler.startMonitoring()

            BatteryLogger.d(TAG, "MEMORY_OPTIMIZATION: MemoryOptimizationService started successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "MEMORY_OPTIMIZATION: Failed to start MemoryOptimizationService", e)
        }
    }

    // Lifecycle observer methods
    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    fun onAppForegrounded() {
        applovinAppOpenAdManager.showAppOpenAd("default_aoa")
        Log.d(TAG, "App moved to foreground")

        // DEPRECATED: Legacy battery service check disabled
        // Using only CoreBatteryStatsService for unified battery monitoring
        // if (!batteryStatusServiceHelper.isServiceRunning()) {
        //     Log.d(TAG, "Battery service not running, starting it now")
        //     batteryStatusServiceHelper.startService()
        // }

        if (!coreBatteryServiceHelper.isServiceRunning()) {
            Log.d(TAG, "CoreBatteryStatsService not running, starting it now")
            try {
                coreBatteryServiceHelper.startService()

                // Log service status after foreground startup attempt
                val serviceStatus = coreBatteryServiceHelper.getServiceStatus()
                Log.d(TAG, "CoreBatteryStatsService foreground startup status: $serviceStatus")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restart CoreBatteryStatsService in foreground", e)
            }
        }

        // Check and start ChargingOverlayService if needed and configuration allows
        if (!chargingOverlayServiceHelper.isServiceRunning() && chargingOverlayServiceHelper.shouldStartService()) {
            BatteryLogger.d(TAG, "ChargingOverlayService not running but should be, starting it now")
            chargingOverlayServiceHelper.startServiceIfNeeded()
        }
    }

    /**
     * Starts animation preloading in the background.
     * Downloads the first 6 animation items for improved initial loading performance.
     */
    private fun startAnimationPreloading() {
        BatteryLogger.d(TAG, "Starting animation preloading from Application")

        applicationScope.launch(Dispatchers.IO) {
            var startTime = 0L
            var animations = emptyList<com.tqhit.battery.one.fragment.main.animation.data.AnimationItem>()

            try {
                // Check if animation data is available
                if (!animationDataService.isAnimationDataAvailable()) {
                    BatteryLogger.w(TAG, "Animation data not available, skipping preloading")
                    return@launch
                }

                // Get animations for preloading
                animations = animationDataService.getAnimationsForPreloading()
                if (animations.isEmpty()) {
                    BatteryLogger.w(TAG, "No animations available for preloading")
                    return@launch
                }

                // Log preloading start with monitoring
                preloadingMonitor.logPreloadingStart(animations.size, "App startup")
                startTime = System.currentTimeMillis()

                BatteryLogger.d(TAG, "Initiating preloading for ${animations.size} animations")

                // Start preloading
                val result = animationPreloadingRepository.initiatePreloading(animations)

                // Log preloading completion with performance metrics
                val duration = System.currentTimeMillis() - startTime
                preloadingMonitor.logPreloadingCompletion(result, duration, animations.size)

                // Log result
                when (result) {
                    is com.tqhit.battery.one.repository.PreloadingResult.Success -> {
                        BatteryLogger.d(TAG, "Animation preloading completed successfully: ${result.preloadedCount} animations")
                    }
                    is com.tqhit.battery.one.repository.PreloadingResult.PartialSuccess -> {
                        BatteryLogger.w(TAG, "Animation preloading partially successful: ${result.successCount} succeeded, ${result.failures.size} failed")
                    }
                    is com.tqhit.battery.one.repository.PreloadingResult.AllFailed -> {
                        BatteryLogger.e(TAG, "Animation preloading failed for all ${result.failures.size} animations")
                    }
                    is com.tqhit.battery.one.repository.PreloadingResult.AlreadyUpToDate -> {
                        BatteryLogger.d(TAG, "Animation preloading skipped - already up to date")
                    }
                    is com.tqhit.battery.one.repository.PreloadingResult.NoAnimationsProvided -> {
                        BatteryLogger.w(TAG, "Animation preloading skipped - no animations provided")
                    }
                    is com.tqhit.battery.one.repository.PreloadingResult.Error -> {
                        BatteryLogger.e(TAG, "Animation preloading error", result.exception)
                    }
                }

                // Log current preloading statistics for monitoring
                preloadingMonitor.logCurrentStats()

            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Unexpected error during animation preloading", e)
                // Log the error with monitoring for tracking
                val duration = System.currentTimeMillis() - startTime
                preloadingMonitor.logPreloadingCompletion(
                    com.tqhit.battery.one.repository.PreloadingResult.Error(e),
                    duration,
                    animations.size
                )
            }
        }
    }


}