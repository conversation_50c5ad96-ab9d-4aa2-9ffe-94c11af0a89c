package com.tqhit.battery.one.utils

import android.content.Context
import android.os.Process
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.manager.quota.StorageQuotaManager
import com.tqhit.battery.one.service.thumbnail.MemoryAwareThumbnailPreloader
import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * CPU-optimized preloader for TJ_BatteryOne app.
 * Minimizes CPU usage during preload operations with intelligent thread management,
 * background processing optimization, and CPU usage monitoring.
 * 
 * CPU Optimization Features:
 * - Background thread pool with low priority
 * - Adaptive batch sizing based on CPU load
 * - CPU usage monitoring and throttling
 * - Intelligent scheduling during idle periods
 * - Thread pool optimization for different device capabilities
 */
@Singleton
class CpuOptimizedPreloader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val memoryAwareThumbnailPreloader: MemoryAwareThumbnailPreloader,
    private val animationPreloadingRepository: AnimationPreloadingRepository,
    private val storageQuotaManager: StorageQuotaManager,
    private val memoryAnalyzer: MemoryAnalyzer
) {
    companion object {
        private const val TAG = "CpuOptimizedPreloader"
        
        // CPU optimization constants
        private const val LOW_PRIORITY_THREAD_COUNT = 2
        private const val NORMAL_PRIORITY_THREAD_COUNT = 4
        private const val HIGH_PRIORITY_THREAD_COUNT = 1
        
        // CPU monitoring thresholds
        private const val HIGH_CPU_THRESHOLD = 80.0 // 80% CPU usage
        private const val MODERATE_CPU_THRESHOLD = 60.0 // 60% CPU usage
        private const val LOW_CPU_THRESHOLD = 40.0 // 40% CPU usage
        
        // Batch size adaptation
        private const val MAX_BATCH_SIZE = 8
        private const val NORMAL_BATCH_SIZE = 4
        private const val MIN_BATCH_SIZE = 1
        
        // Timing constants
        private const val CPU_MONITOR_INTERVAL_MS = 5000L // 5 seconds
        private const val IDLE_DETECTION_THRESHOLD_MS = 10000L // 10 seconds
        private const val THROTTLE_DELAY_MS = 1000L // 1 second
    }
    
    // Thread pools for different priority levels
    private val lowPriorityExecutor = Executors.newFixedThreadPool(
        LOW_PRIORITY_THREAD_COUNT,
        LowPriorityThreadFactory("LowPriorityPreload")
    )
    
    private val normalPriorityExecutor = Executors.newFixedThreadPool(
        NORMAL_PRIORITY_THREAD_COUNT,
        NormalPriorityThreadFactory("NormalPriorityPreload")
    )
    
    private val highPriorityExecutor = Executors.newFixedThreadPool(
        HIGH_PRIORITY_THREAD_COUNT,
        HighPriorityThreadFactory("HighPriorityPreload")
    )
    
    // Coroutine dispatchers
    private val lowPriorityDispatcher = lowPriorityExecutor.asCoroutineDispatcher()
    private val normalPriorityDispatcher = normalPriorityExecutor.asCoroutineDispatcher()
    private val highPriorityDispatcher = highPriorityExecutor.asCoroutineDispatcher()
    
    // CPU monitoring
    private val cpuMonitorScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val _currentCpuUsage = MutableStateFlow(0.0)
    val currentCpuUsage: StateFlow<Double> = _currentCpuUsage
    
    private val _isThrottled = MutableStateFlow(false)
    val isThrottled: StateFlow<Boolean> = _isThrottled
    
    // Operation queues
    private val highPriorityQueue = Channel<PreloadOperation>(Channel.UNLIMITED)
    private val normalPriorityQueue = Channel<PreloadOperation>(Channel.UNLIMITED)
    private val lowPriorityQueue = Channel<PreloadOperation>(Channel.UNLIMITED)
    
    // Statistics
    private val operationsCompleted = AtomicLong(0)
    private val operationsThrottled = AtomicLong(0)
    private val totalCpuTime = AtomicLong(0)
    
    /**
     * Preload operation types.
     */
    sealed class PreloadOperation {
        data class AnimationPreload(
            val animations: List<AnimationItem>,
            val priority: Priority
        ) : PreloadOperation()
        
        data class ThumbnailPreload(
            val thumbnails: List<ThumbnailItem>,
            val priority: Priority
        ) : PreloadOperation()
    }
    
    /**
     * Operation priority levels.
     */
    enum class Priority {
        HIGH,    // User-initiated, immediate need
        NORMAL,  // Standard preloading
        LOW      // Background optimization
    }
    
    /**
     * CPU usage levels for adaptive behavior.
     */
    enum class CpuUsageLevel {
        LOW,
        MODERATE,
        HIGH,
        CRITICAL
    }
    
    init {
        startCpuMonitoring()
        startOperationProcessing()
    }
    
    /**
     * Preloads animations with CPU optimization.
     */
    suspend fun preloadAnimationsOptimized(
        animations: List<AnimationItem>,
        priority: Priority = Priority.NORMAL
    ): Boolean = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "CPU_OPTIMIZED_PRELOAD: Queuing ${animations.size} animations with priority $priority")
        
        val operation = PreloadOperation.AnimationPreload(animations, priority)
        
        return@withContext when (priority) {
            Priority.HIGH -> {
                highPriorityQueue.trySend(operation).isSuccess
            }
            Priority.NORMAL -> {
                normalPriorityQueue.trySend(operation).isSuccess
            }
            Priority.LOW -> {
                lowPriorityQueue.trySend(operation).isSuccess
            }
        }
    }
    
    /**
     * Preloads thumbnails with CPU optimization.
     */
    suspend fun preloadThumbnailsOptimized(
        thumbnails: List<ThumbnailItem>,
        priority: Priority = Priority.NORMAL
    ): Boolean = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "CPU_OPTIMIZED_PRELOAD: Queuing ${thumbnails.size} thumbnails with priority $priority")
        
        val operation = PreloadOperation.ThumbnailPreload(thumbnails, priority)
        
        return@withContext when (priority) {
            Priority.HIGH -> {
                highPriorityQueue.trySend(operation).isSuccess
            }
            Priority.NORMAL -> {
                normalPriorityQueue.trySend(operation).isSuccess
            }
            Priority.LOW -> {
                lowPriorityQueue.trySend(operation).isSuccess
            }
        }
    }
    
    /**
     * Starts CPU monitoring for adaptive behavior.
     */
    private fun startCpuMonitoring() {
        cpuMonitorScope.launch {
            while (true) {
                try {
                    val cpuUsage = getCurrentCpuUsage()
                    _currentCpuUsage.value = cpuUsage
                    
                    // Update throttling state based on CPU usage
                    val shouldThrottle = cpuUsage > HIGH_CPU_THRESHOLD
                    if (_isThrottled.value != shouldThrottle) {
                        _isThrottled.value = shouldThrottle
                        BatteryLogger.d(TAG, "CPU_MONITOR: Throttling ${if (shouldThrottle) "enabled" else "disabled"} - CPU: ${cpuUsage}%")
                    }
                    
                    delay(CPU_MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    BatteryLogger.e(TAG, "CPU_MONITOR: Error monitoring CPU usage", e)
                    delay(CPU_MONITOR_INTERVAL_MS)
                }
            }
        }
    }
    
    /**
     * Starts operation processing with priority handling.
     */
    private fun startOperationProcessing() {
        // High priority processor
        cpuMonitorScope.launch(highPriorityDispatcher) {
            for (operation in highPriorityQueue) {
                processOperation(operation, Priority.HIGH)
            }
        }
        
        // Normal priority processor
        cpuMonitorScope.launch(normalPriorityDispatcher) {
            for (operation in normalPriorityQueue) {
                if (!_isThrottled.value) {
                    processOperation(operation, Priority.NORMAL)
                } else {
                    // Requeue to low priority during throttling
                    lowPriorityQueue.trySend(operation)
                    operationsThrottled.incrementAndGet()
                }
            }
        }
        
        // Low priority processor
        cpuMonitorScope.launch(lowPriorityDispatcher) {
            for (operation in lowPriorityQueue) {
                // Only process low priority when CPU usage is acceptable
                if (_currentCpuUsage.value < MODERATE_CPU_THRESHOLD) {
                    processOperation(operation, Priority.LOW)
                } else {
                    // Delay processing during high CPU usage
                    delay(THROTTLE_DELAY_MS)
                    lowPriorityQueue.trySend(operation) // Requeue
                }
            }
        }
    }
    
    /**
     * Processes a preload operation with CPU optimization.
     */
    private suspend fun processOperation(operation: PreloadOperation, priority: Priority) {
        val startTime = System.currentTimeMillis()
        val startCpuTime = Process.getElapsedCpuTime()
        
        try {
            BatteryLogger.d(TAG, "CPU_PROCESS: Starting operation with priority $priority")
            
            // Check storage quota before processing
            val quotaCheck = when (operation) {
                is PreloadOperation.AnimationPreload -> {
                    val estimatedSize = operation.animations.size * 5L // Estimate 5MB per animation
                    storageQuotaManager.isOperationAllowed(estimatedSize, "video_preload")
                }
                is PreloadOperation.ThumbnailPreload -> {
                    val estimatedSize = operation.thumbnails.size * 1L // Estimate 1MB per thumbnail
                    storageQuotaManager.isOperationAllowed(estimatedSize, "thumbnail_preload")
                }
            }
            
            if (!quotaCheck) {
                BatteryLogger.w(TAG, "CPU_PROCESS: Operation blocked by storage quota")
                return
            }
            
            // Adaptive batch processing based on CPU usage and priority
            val batchSize = calculateOptimalBatchSize(priority)
            
            when (operation) {
                is PreloadOperation.AnimationPreload -> {
                    processAnimationBatches(operation.animations, batchSize, priority)
                }
                is PreloadOperation.ThumbnailPreload -> {
                    processThumbnailBatches(operation.thumbnails, batchSize, priority)
                }
            }
            
            operationsCompleted.incrementAndGet()
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "CPU_PROCESS: Error processing operation", e)
        } finally {
            val endTime = System.currentTimeMillis()
            val endCpuTime = Process.getElapsedCpuTime()
            val cpuTimeUsed = endCpuTime - startCpuTime
            
            totalCpuTime.addAndGet(cpuTimeUsed)
            
            BatteryLogger.d(TAG, "CPU_PROCESS: Operation completed - Duration: ${endTime - startTime}ms, CPU time: ${cpuTimeUsed}ms")
        }
    }
    
    /**
     * Processes animation batches with CPU optimization.
     */
    private suspend fun processAnimationBatches(
        animations: List<AnimationItem>,
        batchSize: Int,
        priority: Priority
    ) {
        animations.chunked(batchSize).forEach { batch ->
            // Check CPU usage before each batch
            if (_isThrottled.value && priority != Priority.HIGH) {
                BatteryLogger.d(TAG, "CPU_BATCH: Delaying batch due to high CPU usage")
                delay(THROTTLE_DELAY_MS)
            }
            
            // Process batch
            try {
                animationPreloadingRepository.initiatePreloading(batch)
                
                // Small delay between batches to prevent CPU spikes
                if (priority == Priority.LOW) {
                    delay(100) // 100ms delay for low priority
                }
                
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "CPU_BATCH: Error processing animation batch", e)
            }
        }
    }
    
    /**
     * Processes thumbnail batches with CPU optimization.
     */
    private suspend fun processThumbnailBatches(
        thumbnails: List<ThumbnailItem>,
        batchSize: Int,
        priority: Priority
    ) {
        thumbnails.chunked(batchSize).forEach { batch ->
            // Check CPU usage before each batch
            if (_isThrottled.value && priority != Priority.HIGH) {
                BatteryLogger.d(TAG, "CPU_BATCH: Delaying thumbnail batch due to high CPU usage")
                delay(THROTTLE_DELAY_MS)
            }
            
            // Process batch
            try {
                val highPriority = priority == Priority.HIGH
                memoryAwareThumbnailPreloader.preloadThumbnailsMemoryAware(batch, highPriority)
                
                // Small delay between batches to prevent CPU spikes
                if (priority == Priority.LOW) {
                    delay(50) // 50ms delay for low priority thumbnails
                }
                
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "CPU_BATCH: Error processing thumbnail batch", e)
            }
        }
    }
    
    /**
     * Calculates optimal batch size based on CPU usage and priority.
     */
    private fun calculateOptimalBatchSize(priority: Priority): Int {
        val cpuUsage = _currentCpuUsage.value
        
        return when {
            priority == Priority.HIGH -> MAX_BATCH_SIZE // Always use max for high priority
            cpuUsage > HIGH_CPU_THRESHOLD -> MIN_BATCH_SIZE
            cpuUsage > MODERATE_CPU_THRESHOLD -> NORMAL_BATCH_SIZE / 2
            cpuUsage > LOW_CPU_THRESHOLD -> NORMAL_BATCH_SIZE
            else -> MAX_BATCH_SIZE
        }
    }
    
    /**
     * Gets current CPU usage percentage.
     */
    private fun getCurrentCpuUsage(): Double {
        return try {
            // This is a simplified CPU usage calculation
            // In a real implementation, you might use more sophisticated methods
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory

            // Approximate CPU usage based on memory pressure and system load
            val memoryUsagePercent = (usedMemory.toDouble() / totalMemory.toDouble()) * 100

            // This is a rough approximation - real CPU monitoring would require native code
            minOf(memoryUsagePercent * 0.8, 100.0)

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "CPU_USAGE: Error calculating CPU usage", e)
            0.0
        }
    }

    /**
     * Gets CPU optimization statistics.
     */
    fun getCpuOptimizationStats(): Map<String, Any> {
        return mapOf(
            "operations_completed" to operationsCompleted.get(),
            "operations_throttled" to operationsThrottled.get(),
            "total_cpu_time_ms" to totalCpuTime.get(),
            "current_cpu_usage_percent" to _currentCpuUsage.value,
            "is_throttled" to _isThrottled.value,
            "high_priority_queue_size" to highPriorityQueue.tryReceive().isFailure,
            "normal_priority_queue_size" to normalPriorityQueue.tryReceive().isFailure,
            "low_priority_queue_size" to lowPriorityQueue.tryReceive().isFailure
        )
    }

    /**
     * Clears all queued operations.
     */
    fun clearAllQueues() {
        // Clear queues by consuming all pending operations
        cpuMonitorScope.launch {
            while (!highPriorityQueue.isEmpty) {
                highPriorityQueue.tryReceive()
            }
            while (!normalPriorityQueue.isEmpty) {
                normalPriorityQueue.tryReceive()
            }
            while (!lowPriorityQueue.isEmpty) {
                lowPriorityQueue.tryReceive()
            }
        }

        BatteryLogger.d(TAG, "CPU_QUEUES: All queues cleared")
    }

    /**
     * Shuts down the CPU optimized preloader.
     */
    fun shutdown() {
        BatteryLogger.d(TAG, "CPU_SHUTDOWN: Shutting down CPU optimized preloader")

        cpuMonitorScope.cancel()

        lowPriorityExecutor.shutdown()
        normalPriorityExecutor.shutdown()
        highPriorityExecutor.shutdown()

        highPriorityQueue.close()
        normalPriorityQueue.close()
        lowPriorityQueue.close()
    }
}

/**
 * Thread factory for low priority background threads.
 */
private class LowPriorityThreadFactory(private val namePrefix: String) : ThreadFactory {
    private val threadNumber = AtomicInteger(1)

    override fun newThread(r: Runnable): Thread {
        val thread = Thread(r, "$namePrefix-${threadNumber.getAndIncrement()}")
        thread.isDaemon = true
        thread.priority = Thread.MIN_PRIORITY
        return thread
    }
}

/**
 * Thread factory for normal priority threads.
 */
private class NormalPriorityThreadFactory(private val namePrefix: String) : ThreadFactory {
    private val threadNumber = AtomicInteger(1)

    override fun newThread(r: Runnable): Thread {
        val thread = Thread(r, "$namePrefix-${threadNumber.getAndIncrement()}")
        thread.isDaemon = true
        thread.priority = Thread.NORM_PRIORITY
        return thread
    }
}

/**
 * Thread factory for high priority threads.
 */
private class HighPriorityThreadFactory(private val namePrefix: String) : ThreadFactory {
    private val threadNumber = AtomicInteger(1)

    override fun newThread(r: Runnable): Thread {
        val thread = Thread(r, "$namePrefix-${threadNumber.getAndIncrement()}")
        thread.isDaemon = false // High priority threads should not be daemon
        thread.priority = Thread.MAX_PRIORITY
        return thread
    }
}
