package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideOptimizedStorageManagerFactory implements Factory<OptimizedStorageManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationFileManager> animationFileManagerProvider;

  private final Provider<ThumbnailFileManager> thumbnailFileManagerProvider;

  public ThumbnailPreloadingModule_ProvideOptimizedStorageManagerFactory(
      Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider) {
    this.contextProvider = contextProvider;
    this.animationFileManagerProvider = animationFileManagerProvider;
    this.thumbnailFileManagerProvider = thumbnailFileManagerProvider;
  }

  @Override
  public OptimizedStorageManager get() {
    return provideOptimizedStorageManager(contextProvider.get(), animationFileManagerProvider.get(), thumbnailFileManagerProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideOptimizedStorageManagerFactory create(
      Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider) {
    return new ThumbnailPreloadingModule_ProvideOptimizedStorageManagerFactory(contextProvider, animationFileManagerProvider, thumbnailFileManagerProvider);
  }

  public static OptimizedStorageManager provideOptimizedStorageManager(Context context,
      AnimationFileManager animationFileManager, ThumbnailFileManager thumbnailFileManager) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideOptimizedStorageManager(context, animationFileManager, thumbnailFileManager));
  }
}
