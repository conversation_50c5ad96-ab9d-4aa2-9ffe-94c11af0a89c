package com.tqhit.battery.one.manager.storage

import android.content.Context
import android.os.StatFs
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Optimized storage manager for video and thumbnail caching in TJ_BatteryOne app.
 * Implements intelligent storage management with size limits, lazy loading, and cleanup strategies.
 * 
 * Storage Targets:
 * - Video cache: <50MB total storage
 * - Thumbnail cache: <10MB total storage
 * - Minimum free space: 100MB
 * - Cleanup threshold: 80% of cache limit
 */
@Singleton
class OptimizedStorageManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val animationFileManager: AnimationFileManager,
    private val thumbnailFileManager: ThumbnailFileManager
) {
    companion object {
        private const val TAG = "OptimizedStorageManager"
        
        // Storage limits (in bytes)
        private const val MAX_VIDEO_CACHE_SIZE_BYTES = 50L * 1024 * 1024 // 50MB
        private const val MAX_THUMBNAIL_CACHE_SIZE_BYTES = 10L * 1024 * 1024 // 10MB
        private const val MIN_FREE_SPACE_BYTES = 100L * 1024 * 1024 // 100MB
        
        // Cleanup thresholds
        private const val VIDEO_CLEANUP_THRESHOLD = 0.8 // 80% of limit
        private const val THUMBNAIL_CLEANUP_THRESHOLD = 0.8 // 80% of limit
        
        // File age thresholds for cleanup (in milliseconds)
        private const val OLD_FILE_THRESHOLD_MS = 7 * 24 * 60 * 60 * 1000L // 7 days
        private const val UNUSED_FILE_THRESHOLD_MS = 3 * 24 * 60 * 60 * 1000L // 3 days
        
        // Conversion constants
        private const val BYTES_TO_MB = 1024 * 1024L
    }
    
    // File usage tracking
    private val fileAccessTimes = ConcurrentHashMap<String, Long>()
    private val fileUsageCount = ConcurrentHashMap<String, Int>()
    
    /**
     * Storage analysis result.
     */
    data class StorageAnalysis(
        val videoCacheSizeMB: Long,
        val thumbnailCacheSizeMB: Long,
        val totalCacheSizeMB: Long,
        val availableSpaceMB: Long,
        val isStorageLow: Boolean,
        val needsCleanup: Boolean,
        val videoFileCount: Int,
        val thumbnailFileCount: Int,
        val oldFileCount: Int,
        val unusedFileCount: Int
    )
    
    /**
     * Cleanup result information.
     */
    data class CleanupResult(
        val videosDeleted: Int,
        val thumbnailsDeleted: Int,
        val spaceFreesMB: Long,
        val duration: Long,
        val success: Boolean,
        val error: String? = null
    )
    
    /**
     * Analyzes current storage usage and determines if cleanup is needed.
     */
    suspend fun analyzeStorage(): StorageAnalysis = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS: Starting storage analysis")
        
        val startTime = System.currentTimeMillis()
        
        try {
            val videoDir = animationFileManager.getPreloadDirectory()
            val thumbnailDir = thumbnailFileManager.getThumbnailDirectory()
            
            val videoCacheSize = calculateDirectorySize(videoDir)
            val thumbnailCacheSize = calculateDirectorySize(thumbnailDir)
            val totalCacheSize = videoCacheSize + thumbnailCacheSize
            
            val availableSpace = getAvailableSpace()
            val isStorageLow = availableSpace < MIN_FREE_SPACE_BYTES
            
            val needsVideoCleanup = videoCacheSize > (MAX_VIDEO_CACHE_SIZE_BYTES * VIDEO_CLEANUP_THRESHOLD)
            val needsThumbnailCleanup = thumbnailCacheSize > (MAX_THUMBNAIL_CACHE_SIZE_BYTES * THUMBNAIL_CLEANUP_THRESHOLD)
            val needsCleanup = needsVideoCleanup || needsThumbnailCleanup || isStorageLow
            
            val videoFileCount = videoDir.listFiles()?.size ?: 0
            val thumbnailFileCount = thumbnailDir.listFiles()?.size ?: 0
            
            val (oldFileCount, unusedFileCount) = analyzeFileUsage()
            
            val analysis = StorageAnalysis(
                videoCacheSizeMB = videoCacheSize / BYTES_TO_MB,
                thumbnailCacheSizeMB = thumbnailCacheSize / BYTES_TO_MB,
                totalCacheSizeMB = totalCacheSize / BYTES_TO_MB,
                availableSpaceMB = availableSpace / BYTES_TO_MB,
                isStorageLow = isStorageLow,
                needsCleanup = needsCleanup,
                videoFileCount = videoFileCount,
                thumbnailFileCount = thumbnailFileCount,
                oldFileCount = oldFileCount,
                unusedFileCount = unusedFileCount
            )
            
            val analysisTime = System.currentTimeMillis() - startTime
            BatteryLogger.d(TAG, "STORAGE_ANALYSIS: Analysis completed in ${analysisTime}ms")
            logStorageAnalysis(analysis)
            
            analysis
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "STORAGE_ANALYSIS: Error during storage analysis", e)
            throw e
        }
    }
    
    /**
     * Performs intelligent cleanup based on storage analysis.
     */
    suspend fun performIntelligentCleanup(): CleanupResult = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "STORAGE_CLEANUP: Starting intelligent cleanup")
        
        val startTime = System.currentTimeMillis()
        var videosDeleted = 0
        var thumbnailsDeleted = 0
        var spaceFreesMB = 0L
        
        try {
            val analysis = analyzeStorage()
            
            if (!analysis.needsCleanup) {
                BatteryLogger.d(TAG, "STORAGE_CLEANUP: No cleanup needed")
                return@withContext CleanupResult(0, 0, 0L, System.currentTimeMillis() - startTime, true)
            }
            
            val spaceBeforeCleanup = getAvailableSpace()
            
            // Priority 1: Remove old unused files
            if (analysis.oldFileCount > 0 || analysis.unusedFileCount > 0) {
                val (vDeleted, tDeleted) = cleanupOldAndUnusedFiles()
                videosDeleted += vDeleted
                thumbnailsDeleted += tDeleted
            }
            
            // Priority 2: Clean video cache if over threshold
            if (analysis.videoCacheSizeMB * BYTES_TO_MB > MAX_VIDEO_CACHE_SIZE_BYTES * VIDEO_CLEANUP_THRESHOLD) {
                videosDeleted += cleanupVideoCache()
            }
            
            // Priority 3: Clean thumbnail cache if over threshold
            if (analysis.thumbnailCacheSizeMB * BYTES_TO_MB > MAX_THUMBNAIL_CACHE_SIZE_BYTES * THUMBNAIL_CLEANUP_THRESHOLD) {
                thumbnailsDeleted += cleanupThumbnailCache()
            }
            
            // Priority 4: Emergency cleanup if storage is critically low
            if (analysis.isStorageLow) {
                val (vDeleted, tDeleted) = performEmergencyCleanup()
                videosDeleted += vDeleted
                thumbnailsDeleted += tDeleted
            }
            
            val spaceAfterCleanup = getAvailableSpace()
            spaceFreesMB = (spaceAfterCleanup - spaceBeforeCleanup) / BYTES_TO_MB
            
            val duration = System.currentTimeMillis() - startTime
            val result = CleanupResult(videosDeleted, thumbnailsDeleted, spaceFreesMB, duration, true)
            
            BatteryLogger.d(TAG, "STORAGE_CLEANUP: Cleanup completed - Videos: $videosDeleted, Thumbnails: $thumbnailsDeleted, Space freed: ${spaceFreesMB}MB")
            
            result
            
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            BatteryLogger.e(TAG, "STORAGE_CLEANUP: Error during cleanup", e)
            CleanupResult(videosDeleted, thumbnailsDeleted, spaceFreesMB, duration, false, e.message)
        }
    }
    
    /**
     * Records file access for usage tracking.
     */
    fun recordFileAccess(filePath: String) {
        val currentTime = System.currentTimeMillis()
        fileAccessTimes[filePath] = currentTime
        fileUsageCount[filePath] = (fileUsageCount[filePath] ?: 0) + 1
        
        BatteryLogger.d(TAG, "FILE_ACCESS: Recorded access for $filePath (count: ${fileUsageCount[filePath]})")
    }
    
    /**
     * Checks if storage operation is safe to perform.
     */
    suspend fun isStorageOperationSafe(requiredSpaceMB: Long): Boolean = withContext(Dispatchers.IO) {
        val availableSpace = getAvailableSpace()
        val requiredSpaceBytes = requiredSpaceMB * BYTES_TO_MB
        val safetyBuffer = MIN_FREE_SPACE_BYTES
        
        val isSafe = availableSpace > (requiredSpaceBytes + safetyBuffer)
        
        BatteryLogger.d(TAG, "STORAGE_SAFETY: Required: ${requiredSpaceMB}MB, Available: ${availableSpace / BYTES_TO_MB}MB, Safe: $isSafe")
        
        isSafe
    }
    
    /**
     * Gets storage recommendations based on current usage.
     */
    suspend fun getStorageRecommendations(): List<String> = withContext(Dispatchers.IO) {
        val analysis = analyzeStorage()
        val recommendations = mutableListOf<String>()
        
        if (analysis.isStorageLow) {
            recommendations.add("Critical: Storage space is low (${analysis.availableSpaceMB}MB available)")
            recommendations.add("Perform immediate cleanup to free space")
        }
        
        if (analysis.videoCacheSizeMB > MAX_VIDEO_CACHE_SIZE_BYTES / BYTES_TO_MB) {
            recommendations.add("Video cache is large (${analysis.videoCacheSizeMB}MB) - consider cleanup")
        }
        
        if (analysis.thumbnailCacheSizeMB > MAX_THUMBNAIL_CACHE_SIZE_BYTES / BYTES_TO_MB) {
            recommendations.add("Thumbnail cache is large (${analysis.thumbnailCacheSizeMB}MB) - consider cleanup")
        }
        
        if (analysis.oldFileCount > 0) {
            recommendations.add("${analysis.oldFileCount} old files can be safely removed")
        }
        
        if (analysis.unusedFileCount > 0) {
            recommendations.add("${analysis.unusedFileCount} unused files can be removed to free space")
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("Storage usage is optimal")
        }
        
        recommendations
    }
    
    /**
     * Calculates directory size recursively.
     */
    private fun calculateDirectorySize(directory: File): Long {
        if (!directory.exists() || !directory.isDirectory) return 0L
        
        var size = 0L
        directory.listFiles()?.forEach { file ->
            size += if (file.isDirectory) {
                calculateDirectorySize(file)
            } else {
                file.length()
            }
        }
        return size
    }
    
    /**
     * Gets available storage space.
     */
    private fun getAvailableSpace(): Long {
        val statFs = StatFs(context.filesDir.path)
        return statFs.availableBlocksLong * statFs.blockSizeLong
    }
    
    /**
     * Analyzes file usage patterns.
     */
    private fun analyzeFileUsage(): Pair<Int, Int> {
        val currentTime = System.currentTimeMillis()
        var oldFileCount = 0
        var unusedFileCount = 0
        
        fileAccessTimes.forEach { (filePath, lastAccess) ->
            val fileAge = currentTime - lastAccess
            val usageCount = fileUsageCount[filePath] ?: 0
            
            if (fileAge > OLD_FILE_THRESHOLD_MS) {
                oldFileCount++
            }
            
            if (fileAge > UNUSED_FILE_THRESHOLD_MS && usageCount <= 1) {
                unusedFileCount++
            }
        }
        
        return Pair(oldFileCount, unusedFileCount)
    }
    
    /**
     * Cleans up old and unused files based on usage tracking.
     */
    private suspend fun cleanupOldAndUnusedFiles(): Pair<Int, Int> = withContext(Dispatchers.IO) {
        var videosDeleted = 0
        var thumbnailsDeleted = 0
        val currentTime = System.currentTimeMillis()

        BatteryLogger.d(TAG, "CLEANUP_OLD_UNUSED: Starting cleanup of old and unused files")

        // Clean up old video files
        val videoDir = animationFileManager.getPreloadDirectory()
        videoDir.listFiles()?.forEach { file ->
            val filePath = file.absolutePath
            val lastAccess = fileAccessTimes[filePath] ?: file.lastModified()
            val usageCount = fileUsageCount[filePath] ?: 0
            val fileAge = currentTime - lastAccess

            val shouldDelete = fileAge > OLD_FILE_THRESHOLD_MS ||
                             (fileAge > UNUSED_FILE_THRESHOLD_MS && usageCount <= 1)

            if (shouldDelete && file.delete()) {
                videosDeleted++
                fileAccessTimes.remove(filePath)
                fileUsageCount.remove(filePath)
                BatteryLogger.d(TAG, "CLEANUP_OLD_UNUSED: Deleted old/unused video: ${file.name}")
            }
        }

        // Clean up old thumbnail files
        val thumbnailDir = thumbnailFileManager.getThumbnailDirectory()
        thumbnailDir.listFiles()?.forEach { file ->
            val filePath = file.absolutePath
            val lastAccess = fileAccessTimes[filePath] ?: file.lastModified()
            val usageCount = fileUsageCount[filePath] ?: 0
            val fileAge = currentTime - lastAccess

            val shouldDelete = fileAge > OLD_FILE_THRESHOLD_MS ||
                             (fileAge > UNUSED_FILE_THRESHOLD_MS && usageCount <= 1)

            if (shouldDelete && file.delete()) {
                thumbnailsDeleted++
                fileAccessTimes.remove(filePath)
                fileUsageCount.remove(filePath)
                BatteryLogger.d(TAG, "CLEANUP_OLD_UNUSED: Deleted old/unused thumbnail: ${file.name}")
            }
        }

        BatteryLogger.d(TAG, "CLEANUP_OLD_UNUSED: Completed - Videos: $videosDeleted, Thumbnails: $thumbnailsDeleted")
        Pair(videosDeleted, thumbnailsDeleted)
    }

    /**
     * Cleans up video cache using LRU strategy.
     */
    private suspend fun cleanupVideoCache(): Int = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "CLEANUP_VIDEO: Starting video cache cleanup")

        val videoDir = animationFileManager.getPreloadDirectory()
        val files = videoDir.listFiles() ?: return@withContext 0

        // Sort files by last access time (LRU first)
        val sortedFiles = files.sortedBy { file ->
            fileAccessTimes[file.absolutePath] ?: file.lastModified()
        }

        var deletedCount = 0
        var currentSize = calculateDirectorySize(videoDir)
        val targetSize = (MAX_VIDEO_CACHE_SIZE_BYTES * 0.7).toLong() // Clean to 70% of limit

        for (file in sortedFiles) {
            if (currentSize <= targetSize) break

            val fileSize = file.length()
            if (file.delete()) {
                deletedCount++
                currentSize -= fileSize
                fileAccessTimes.remove(file.absolutePath)
                fileUsageCount.remove(file.absolutePath)
                BatteryLogger.d(TAG, "CLEANUP_VIDEO: Deleted video file: ${file.name} (${fileSize / BYTES_TO_MB}MB)")
            }
        }

        BatteryLogger.d(TAG, "CLEANUP_VIDEO: Completed - Deleted $deletedCount files")
        deletedCount
    }

    /**
     * Cleans up thumbnail cache using LRU strategy.
     */
    private suspend fun cleanupThumbnailCache(): Int = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "CLEANUP_THUMBNAIL: Starting thumbnail cache cleanup")

        val thumbnailDir = thumbnailFileManager.getThumbnailDirectory()
        val files = thumbnailDir.listFiles() ?: return@withContext 0

        // Sort files by last access time (LRU first)
        val sortedFiles = files.sortedBy { file ->
            fileAccessTimes[file.absolutePath] ?: file.lastModified()
        }

        var deletedCount = 0
        var currentSize = calculateDirectorySize(thumbnailDir)
        val targetSize = (MAX_THUMBNAIL_CACHE_SIZE_BYTES * 0.7).toLong() // Clean to 70% of limit

        for (file in sortedFiles) {
            if (currentSize <= targetSize) break

            val fileSize = file.length()
            if (file.delete()) {
                deletedCount++
                currentSize -= fileSize
                fileAccessTimes.remove(file.absolutePath)
                fileUsageCount.remove(file.absolutePath)
                BatteryLogger.d(TAG, "CLEANUP_THUMBNAIL: Deleted thumbnail file: ${file.name}")
            }
        }

        BatteryLogger.d(TAG, "CLEANUP_THUMBNAIL: Completed - Deleted $deletedCount files")
        deletedCount
    }

    /**
     * Performs emergency cleanup when storage is critically low.
     */
    private suspend fun performEmergencyCleanup(): Pair<Int, Int> = withContext(Dispatchers.IO) {
        BatteryLogger.w(TAG, "EMERGENCY_CLEANUP: Starting emergency cleanup due to low storage")

        var videosDeleted = 0
        var thumbnailsDeleted = 0

        // More aggressive cleanup - remove 50% of files
        val videoDir = animationFileManager.getPreloadDirectory()
        val videoFiles = videoDir.listFiles()?.sortedBy {
            fileAccessTimes[it.absolutePath] ?: it.lastModified()
        } ?: emptyList()

        val videosToDelete = videoFiles.take(videoFiles.size / 2)
        videosToDelete.forEach { file ->
            if (file.delete()) {
                videosDeleted++
                fileAccessTimes.remove(file.absolutePath)
                fileUsageCount.remove(file.absolutePath)
            }
        }

        val thumbnailDir = thumbnailFileManager.getThumbnailDirectory()
        val thumbnailFiles = thumbnailDir.listFiles()?.sortedBy {
            fileAccessTimes[it.absolutePath] ?: it.lastModified()
        } ?: emptyList()

        val thumbnailsToDelete = thumbnailFiles.take(thumbnailFiles.size / 2)
        thumbnailsToDelete.forEach { file ->
            if (file.delete()) {
                thumbnailsDeleted++
                fileAccessTimes.remove(file.absolutePath)
                fileUsageCount.remove(file.absolutePath)
            }
        }

        BatteryLogger.w(TAG, "EMERGENCY_CLEANUP: Completed - Videos: $videosDeleted, Thumbnails: $thumbnailsDeleted")
        Pair(videosDeleted, thumbnailsDeleted)
    }

    /**
     * Logs storage analysis results.
     */
    private fun logStorageAnalysis(analysis: StorageAnalysis) {
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: ===== Storage Analysis Report =====")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Video Cache: ${analysis.videoCacheSizeMB}MB (${analysis.videoFileCount} files)")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Thumbnail Cache: ${analysis.thumbnailCacheSizeMB}MB (${analysis.thumbnailFileCount} files)")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Total Cache: ${analysis.totalCacheSizeMB}MB")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Available Space: ${analysis.availableSpaceMB}MB")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Storage Low: ${analysis.isStorageLow}")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Needs Cleanup: ${analysis.needsCleanup}")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Old Files: ${analysis.oldFileCount}")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: Unused Files: ${analysis.unusedFileCount}")
        BatteryLogger.d(TAG, "STORAGE_ANALYSIS_RESULTS: ===== End Analysis Report =====")
    }
}
