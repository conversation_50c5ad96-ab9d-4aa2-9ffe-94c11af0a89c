@echo off
REM TJ_BatteryOne Memory Profiling and Performance Validation Script
REM Tests memory usage patterns, performance benchmarks, and validates optimization effectiveness
REM Usage: memory_profiling_test.bat [device_id]

setlocal enabledelayedexpansion

REM Configuration
set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set DEVICE_ID=%1
if "%DEVICE_ID%"=="" set DEVICE_ID=emulator-5554

REM Test configuration
set TEST_DURATION=300
set MEMORY_SAMPLE_INTERVAL=5
set PERFORMANCE_THRESHOLD_COLD_START=3000
set PERFORMANCE_THRESHOLD_FRAGMENT_SWITCH=500
set PERFORMANCE_THRESHOLD_DATA_FLOW=100
set MEMORY_THRESHOLD_MB=100

echo ========================================
echo TJ_BatteryOne Memory Profiling Test
echo ========================================
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo Test Duration: %TEST_DURATION% seconds
echo ========================================

REM Check ADB connection
echo [INFO] Checking ADB connection...
%ADB_PATH% -s %DEVICE_ID% shell echo "Device connected" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Cannot connect to device %DEVICE_ID%
    echo [ERROR] Please ensure device is connected and ADB is working
    exit /b 1
)
echo [SUCCESS] Device %DEVICE_ID% connected

REM Check if app is installed
echo [INFO] Checking if app is installed...
%ADB_PATH% -s %DEVICE_ID% shell pm list packages | findstr %APP_ID% >nul
if errorlevel 1 (
    echo [ERROR] App %APP_ID% is not installed on device
    exit /b 1
)
echo [SUCCESS] App is installed

REM Create output directory
set OUTPUT_DIR=memory_test_results_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set OUTPUT_DIR=%OUTPUT_DIR: =0%
mkdir "%OUTPUT_DIR%" 2>nul

echo [INFO] Test results will be saved to: %OUTPUT_DIR%

REM Start logcat monitoring in background
echo [INFO] Starting logcat monitoring...
start /b %ADB_PATH% -s %DEVICE_ID% logcat -s MEMORY_ANALYSIS:D MEMORY_USAGE:D PERFORMANCE_BENCHMARK:D STARTUP_TIMING:D FRAGMENT_SWITCH:D DATA_FLOW_LATENCY:D > "%OUTPUT_DIR%\logcat_memory.log"

REM Test 1: Cold Start Performance
echo.
echo [TEST 1] Cold Start Performance Test
echo =====================================
echo [INFO] Force stopping app...
%ADB_PATH% -s %DEVICE_ID% shell am force-stop %APP_ID%
timeout /t 2 >nul

echo [INFO] Starting cold start test...
%ADB_PATH% -s %DEVICE_ID% shell am start -W -n %APP_ID%/com.tqhit.battery.one.activity.starting.StartingActivity > "%OUTPUT_DIR%\cold_start_result.txt"

REM Extract cold start time
for /f "tokens=2 delims=:" %%a in ('findstr "TotalTime" "%OUTPUT_DIR%\cold_start_result.txt"') do (
    set COLD_START_TIME=%%a
    set COLD_START_TIME=!COLD_START_TIME: =!
)

echo [RESULT] Cold Start Time: %COLD_START_TIME%ms
if %COLD_START_TIME% GTR %PERFORMANCE_THRESHOLD_COLD_START% (
    echo [WARNING] Cold start time exceeds threshold (%PERFORMANCE_THRESHOLD_COLD_START%ms)
) else (
    echo [SUCCESS] Cold start time within acceptable range
)

REM Wait for app to fully load
timeout /t 5 >nul

REM Test 2: Memory Usage Baseline
echo.
echo [TEST 2] Memory Usage Baseline
echo ==============================
echo [INFO] Collecting baseline memory usage...

REM Get memory info using dumpsys
%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% > "%OUTPUT_DIR%\memory_baseline.txt"

REM Extract PSS memory usage
for /f "tokens=2" %%a in ('findstr "TOTAL" "%OUTPUT_DIR%\memory_baseline.txt"') do (
    set BASELINE_MEMORY=%%a
    goto :baseline_found
)
:baseline_found

echo [RESULT] Baseline Memory Usage: %BASELINE_MEMORY% KB

REM Test 3: Memory Usage During Preloading
echo.
echo [TEST 3] Memory Usage During Preloading
echo =======================================
echo [INFO] Triggering preloading operations...

REM Navigate to animation fragment to trigger preloading
%ADB_PATH% -s %DEVICE_ID% shell input tap 500 1500

REM Monitor memory usage during preloading
echo [INFO] Monitoring memory usage for %TEST_DURATION% seconds...
set /a SAMPLES=%TEST_DURATION% / %MEMORY_SAMPLE_INTERVAL%
set SAMPLE_COUNT=0

:memory_loop
if %SAMPLE_COUNT% GEQ %SAMPLES% goto :memory_done

%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% | findstr "TOTAL" >> "%OUTPUT_DIR%\memory_samples.txt"
timeout /t %MEMORY_SAMPLE_INTERVAL% >nul
set /a SAMPLE_COUNT+=1
goto :memory_loop

:memory_done

echo [INFO] Memory sampling completed

REM Test 4: Fragment Switch Performance
echo.
echo [TEST 4] Fragment Switch Performance
echo ====================================
echo [INFO] Testing fragment switching performance...

REM Switch between fragments and measure performance
for /l %%i in (1,1,5) do (
    echo [INFO] Fragment switch test %%i/5
    %ADB_PATH% -s %DEVICE_ID% shell input tap 200 2000
    timeout /t 1 >nul
    %ADB_PATH% -s %DEVICE_ID% shell input tap 400 2000
    timeout /t 1 >nul
)

REM Test 5: Storage Usage Analysis
echo.
echo [TEST 5] Storage Usage Analysis
echo ===============================
echo [INFO] Analyzing storage usage...

REM Get app storage usage
%ADB_PATH% -s %DEVICE_ID% shell du -sh /data/data/%APP_ID% > "%OUTPUT_DIR%\storage_usage.txt" 2>nul
%ADB_PATH% -s %DEVICE_ID% shell ls -la /data/data/%APP_ID%/files/ > "%OUTPUT_DIR%\files_list.txt" 2>nul

echo [INFO] Storage analysis completed

REM Test 6: Performance Stress Test
echo.
echo [TEST 6] Performance Stress Test
echo ================================
echo [INFO] Running performance stress test...

REM Rapid navigation to stress test the app
for /l %%i in (1,1,20) do (
    %ADB_PATH% -s %DEVICE_ID% shell input tap 300 1500
    timeout /t 1 >nul
    %ADB_PATH% -s %DEVICE_ID% shell input tap 500 1500
    timeout /t 1 >nul
)

echo [INFO] Stress test completed

REM Test 7: Memory Leak Detection
echo.
echo [TEST 7] Memory Leak Detection
echo ==============================
echo [INFO] Checking for memory leaks...

REM Get final memory usage
%ADB_PATH% -s %DEVICE_ID% shell dumpsys meminfo %APP_ID% > "%OUTPUT_DIR%\memory_final.txt"

REM Extract final PSS memory usage
for /f "tokens=2" %%a in ('findstr "TOTAL" "%OUTPUT_DIR%\memory_final.txt"') do (
    set FINAL_MEMORY=%%a
    goto :final_found
)
:final_found

set /a MEMORY_INCREASE=%FINAL_MEMORY% - %BASELINE_MEMORY%
echo [RESULT] Memory increase: %MEMORY_INCREASE% KB

if %MEMORY_INCREASE% GTR 50000 (
    echo [WARNING] Significant memory increase detected - possible memory leak
) else (
    echo [SUCCESS] Memory usage increase within acceptable range
)

REM Stop logcat monitoring
echo [INFO] Stopping logcat monitoring...
taskkill /f /im adb.exe >nul 2>&1

REM Generate summary report
echo.
echo [SUMMARY] Generating test report...
echo ===================================

(
echo TJ_BatteryOne Memory Profiling Test Report
echo Generated: %date% %time%
echo Device: %DEVICE_ID%
echo App ID: %APP_ID%
echo.
echo PERFORMANCE RESULTS:
echo - Cold Start Time: %COLD_START_TIME%ms ^(Threshold: %PERFORMANCE_THRESHOLD_COLD_START%ms^)
echo - Baseline Memory: %BASELINE_MEMORY% KB
echo - Final Memory: %FINAL_MEMORY% KB
echo - Memory Increase: %MEMORY_INCREASE% KB
echo.
echo THRESHOLDS:
echo - Cold Start: %PERFORMANCE_THRESHOLD_COLD_START%ms
echo - Fragment Switch: %PERFORMANCE_THRESHOLD_FRAGMENT_SWITCH%ms
echo - Data Flow: %PERFORMANCE_THRESHOLD_DATA_FLOW%ms
echo - Memory Usage: %MEMORY_THRESHOLD_MB%MB
echo.
echo FILES GENERATED:
echo - logcat_memory.log: Complete logcat output
echo - cold_start_result.txt: Cold start timing details
echo - memory_baseline.txt: Initial memory state
echo - memory_final.txt: Final memory state
echo - memory_samples.txt: Memory usage over time
echo - storage_usage.txt: Storage usage analysis
echo - files_list.txt: App files listing
echo.
echo RECOMMENDATIONS:
if %COLD_START_TIME% GTR %PERFORMANCE_THRESHOLD_COLD_START% echo - Optimize cold start performance
if %MEMORY_INCREASE% GTR 50000 echo - Investigate potential memory leaks
echo - Review logcat for performance violations
echo - Monitor memory usage patterns in memory_samples.txt
echo - Check storage usage for cache optimization opportunities
) > "%OUTPUT_DIR%\test_report.txt"

echo [SUCCESS] Test completed successfully!
echo [INFO] Results saved to: %OUTPUT_DIR%
echo [INFO] Review test_report.txt for summary
echo [INFO] Check logcat_memory.log for detailed performance logs

REM Open results folder
start "" "%OUTPUT_DIR%"

endlocal
pause
