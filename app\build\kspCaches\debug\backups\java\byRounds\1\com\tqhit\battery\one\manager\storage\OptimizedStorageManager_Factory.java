package com.tqhit.battery.one.manager.storage;

import android.content.Context;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class OptimizedStorageManager_Factory implements Factory<OptimizedStorageManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationFileManager> animationFileManagerProvider;

  private final Provider<ThumbnailFileManager> thumbnailFileManagerProvider;

  public OptimizedStorageManager_Factory(Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider) {
    this.contextProvider = contextProvider;
    this.animationFileManagerProvider = animationFileManagerProvider;
    this.thumbnailFileManagerProvider = thumbnailFileManagerProvider;
  }

  @Override
  public OptimizedStorageManager get() {
    return newInstance(contextProvider.get(), animationFileManagerProvider.get(), thumbnailFileManagerProvider.get());
  }

  public static OptimizedStorageManager_Factory create(Provider<Context> contextProvider,
      Provider<AnimationFileManager> animationFileManagerProvider,
      Provider<ThumbnailFileManager> thumbnailFileManagerProvider) {
    return new OptimizedStorageManager_Factory(contextProvider, animationFileManagerProvider, thumbnailFileManagerProvider);
  }

  public static OptimizedStorageManager newInstance(Context context,
      AnimationFileManager animationFileManager, ThumbnailFileManager thumbnailFileManager) {
    return new OptimizedStorageManager(context, animationFileManager, thumbnailFileManager);
  }
}
