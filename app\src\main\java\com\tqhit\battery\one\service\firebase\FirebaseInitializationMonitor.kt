package com.tqhit.battery.one.service.firebase

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for monitoring Firebase Remote Config initialization status.
 * Provides centralized access to Firebase config state and handles retry logic.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles Firebase Remote Config monitoring
 * - Open/Closed: Extensible for different Firebase services
 * - Dependency Inversion: Depends on abstractions (FirebaseRemoteConfigHelper)
 */
@Singleton
class FirebaseInitializationMonitor @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper
) {
    companion object {
        private const val TAG = "FirebaseInitMonitor"
        private const val MAX_RETRY_ATTEMPTS = 5
        private const val INITIAL_RETRY_DELAY_MS = 1000L
        private const val MAX_RETRY_DELAY_MS = 10000L
        private const val CONFIG_CHECK_TIMEOUT_MS = 30000L
        private const val ANIMATION_JSON_KEY = "animation_json"
        private const val MIN_ANIMATION_JSON_LENGTH = 100 // Minimum expected length for valid JSON
    }
    
    /**
     * Represents the current state of Firebase Remote Config initialization.
     */
    sealed class FirebaseConfigState {
        object NotInitialized : FirebaseConfigState()
        object Initializing : FirebaseConfigState()
        object Ready : FirebaseConfigState()
        data class Failed(val error: String, val retryCount: Int) : FirebaseConfigState()
        object TimedOut : FirebaseConfigState()
    }
    
    /**
     * Callback interface for components that depend on Firebase Remote Config.
     */
    interface FirebaseConfigCallback {
        fun onConfigReady()
        fun onConfigFailed(error: String, shouldUseFallback: Boolean)
        fun onConfigTimeout()
    }
    
    private val _configState = MutableStateFlow<FirebaseConfigState>(FirebaseConfigState.NotInitialized)
    val configState: StateFlow<FirebaseConfigState> = _configState.asStateFlow()
    
    private val callbacks = mutableSetOf<FirebaseConfigCallback>()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var monitoringJob: Job? = null
    private var retryCount = 0
    
    /**
     * Starts monitoring Firebase Remote Config initialization.
     * Should be called during app startup.
     */
    fun startMonitoring() {
        if (monitoringJob?.isActive == true) {
            BatteryLogger.d(TAG, "FIREBASE_MONITOR: Monitoring already active")
            return
        }
        
        BatteryLogger.d(TAG, "FIREBASE_MONITOR: Starting Firebase Remote Config monitoring")
        _configState.value = FirebaseConfigState.Initializing
        retryCount = 0
        
        monitoringJob = scope.launch {
            try {
                val startTime = System.currentTimeMillis()
                var isConfigReady = false
                
                while (!isConfigReady && 
                       (System.currentTimeMillis() - startTime) < CONFIG_CHECK_TIMEOUT_MS &&
                       retryCount < MAX_RETRY_ATTEMPTS) {
                    
                    BatteryLogger.d(TAG, "FIREBASE_MONITOR: Checking config readiness (attempt ${retryCount + 1})")
                    
                    if (checkConfigReadiness()) {
                        isConfigReady = true
                        _configState.value = FirebaseConfigState.Ready
                        BatteryLogger.d(TAG, "FIREBASE_MONITOR: Config ready after ${System.currentTimeMillis() - startTime}ms")
                        notifyCallbacks { it.onConfigReady() }
                    } else {
                        retryCount++
                        val delayMs = calculateRetryDelay(retryCount)
                        
                        BatteryLogger.d(TAG, "FIREBASE_MONITOR: Config not ready, retrying in ${delayMs}ms (attempt $retryCount)")
                        _configState.value = FirebaseConfigState.Failed("Config not ready", retryCount)
                        
                        delay(delayMs)
                    }
                }
                
                if (!isConfigReady) {
                    val totalTime = System.currentTimeMillis() - startTime
                    if (totalTime >= CONFIG_CHECK_TIMEOUT_MS) {
                        BatteryLogger.w(TAG, "FIREBASE_MONITOR: Timeout after ${totalTime}ms")
                        _configState.value = FirebaseConfigState.TimedOut
                        notifyCallbacks { it.onConfigTimeout() }
                    } else {
                        BatteryLogger.w(TAG, "FIREBASE_MONITOR: Max retry attempts reached")
                        _configState.value = FirebaseConfigState.Failed("Max retries exceeded", retryCount)
                        notifyCallbacks { it.onConfigFailed("Max retry attempts reached", true) }
                    }
                }
                
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "FIREBASE_MONITOR: Error during monitoring", e)
                _configState.value = FirebaseConfigState.Failed(e.message ?: "Unknown error", retryCount)
                notifyCallbacks { it.onConfigFailed(e.message ?: "Unknown error", true) }
            }
        }
    }
    
    /**
     * Registers a callback to be notified when Firebase Remote Config state changes.
     */
    fun registerCallback(callback: FirebaseConfigCallback) {
        callbacks.add(callback)
        
        // If config is already ready, notify immediately
        if (_configState.value is FirebaseConfigState.Ready) {
            callback.onConfigReady()
        }
    }
    
    /**
     * Unregisters a previously registered callback.
     */
    fun unregisterCallback(callback: FirebaseConfigCallback) {
        callbacks.remove(callback)
    }
    
    /**
     * Checks if Firebase Remote Config is currently ready.
     */
    fun isConfigReady(): Boolean {
        return _configState.value is FirebaseConfigState.Ready
    }
    
    /**
     * Forces a retry of Firebase Remote Config initialization.
     * Useful for manual retry scenarios.
     */
    fun forceRetry() {
        BatteryLogger.d(TAG, "FIREBASE_MONITOR: Force retry requested")
        retryCount = 0
        startMonitoring()
    }
    
    /**
     * Stops monitoring Firebase Remote Config.
     * Should be called during app shutdown.
     */
    fun stopMonitoring() {
        BatteryLogger.d(TAG, "FIREBASE_MONITOR: Stopping monitoring")
        monitoringJob?.cancel()
        callbacks.clear()
        _configState.value = FirebaseConfigState.NotInitialized
    }

    /**
     * Checks if Firebase Remote Config is ready by validating key configuration values.
     */
    private suspend fun checkConfigReadiness(): Boolean = withContext(Dispatchers.IO) {
        try {
            // Check if animation_json is available and valid
            val animationJson = remoteConfigHelper.getString(ANIMATION_JSON_KEY)
            val isAnimationJsonValid = animationJson.isNotBlank() &&
                                     animationJson.length > MIN_ANIMATION_JSON_LENGTH &&
                                     animationJson.startsWith("[") &&
                                     animationJson.endsWith("]")

            // Check if other critical config values are available
            val isBannerConfigAvailable = try {
                remoteConfigHelper.getBoolean("bn_enable")
                true
            } catch (e: Exception) {
                false
            }

            val isAdConfigAvailable = try {
                remoteConfigHelper.getBoolean("aoa_enable")
                true
            } catch (e: Exception) {
                false
            }

            val isReady = isAnimationJsonValid && isBannerConfigAvailable && isAdConfigAvailable

            BatteryLogger.d(TAG, "FIREBASE_MONITOR: Config readiness check - " +
                    "Animation JSON valid: $isAnimationJsonValid, " +
                    "Banner config: $isBannerConfigAvailable, " +
                    "Ad config: $isAdConfigAvailable, " +
                    "Overall ready: $isReady")

            isReady
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "FIREBASE_MONITOR: Error checking config readiness: ${e.message}")
            false
        }
    }

    /**
     * Calculates exponential backoff delay for retry attempts.
     */
    private fun calculateRetryDelay(attempt: Int): Long {
        val delay = INITIAL_RETRY_DELAY_MS * (1L shl (attempt - 1))
        return minOf(delay, MAX_RETRY_DELAY_MS)
    }

    /**
     * Notifies all registered callbacks with the provided action.
     */
    private fun notifyCallbacks(action: (FirebaseConfigCallback) -> Unit) {
        callbacks.forEach { callback ->
            try {
                action(callback)
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "FIREBASE_MONITOR: Error notifying callback", e)
            }
        }
    }
}
