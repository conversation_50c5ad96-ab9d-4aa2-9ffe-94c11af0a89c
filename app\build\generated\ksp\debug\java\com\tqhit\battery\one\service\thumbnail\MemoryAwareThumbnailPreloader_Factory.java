package com.tqhit.battery.one.service.thumbnail;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MemoryAwareThumbnailPreloader_Factory implements Factory<MemoryAwareThumbnailPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<ThumbnailFileManager> fileManagerProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  public MemoryAwareThumbnailPreloader_Factory(Provider<Context> contextProvider,
      Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    this.contextProvider = contextProvider;
    this.fileManagerProvider = fileManagerProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
  }

  @Override
  public MemoryAwareThumbnailPreloader get() {
    return newInstance(contextProvider.get(), fileManagerProvider.get(), memoryAnalyzerProvider.get());
  }

  public static MemoryAwareThumbnailPreloader_Factory create(Provider<Context> contextProvider,
      Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    return new MemoryAwareThumbnailPreloader_Factory(contextProvider, fileManagerProvider, memoryAnalyzerProvider);
  }

  public static MemoryAwareThumbnailPreloader newInstance(Context context,
      ThumbnailFileManager fileManager, MemoryAnalyzer memoryAnalyzer) {
    return new MemoryAwareThumbnailPreloader(context, fileManager, memoryAnalyzer);
  }
}
