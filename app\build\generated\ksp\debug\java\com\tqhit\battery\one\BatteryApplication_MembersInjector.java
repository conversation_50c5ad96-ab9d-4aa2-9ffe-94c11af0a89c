package com.tqhit.battery.one;

import com.tqhit.adlib.sdk.AdLibHiltApplication_MembersInjector;
import com.tqhit.adlib.sdk.adjust.AdjustAnalyticsHelper;
import com.tqhit.adlib.sdk.ads.AdmobHelper;
import com.tqhit.adlib.sdk.ads.AppOpenHelper;
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager;
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper;
import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import com.tqhit.battery.one.utils.PerformanceProfiler;
import com.tqhit.battery.one.utils.PreloadingMonitor;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryApplication_MembersInjector implements MembersInjector<BatteryApplication> {
  private final Provider<AdmobHelper> admobHelperProvider;

  private final Provider<AppOpenHelper> appOpenHelperProvider;

  private final Provider<AdjustAnalyticsHelper> analyticsHelperProvider;

  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  private final Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider;

  private final Provider<ApplovinAppOpenAdManager> applovinAppOpenAdManagerProvider;

  private final Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider;

  private final Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider;

  private final Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<PreloadingMonitor> preloadingMonitorProvider;

  private final Provider<DeferredThumbnailPreloadingService> deferredThumbnailPreloadingServiceProvider;

  private final Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  private final Provider<PerformanceProfiler> performanceProfilerProvider;

  public BatteryApplication_MembersInjector(Provider<AdmobHelper> admobHelperProvider,
      Provider<AppOpenHelper> appOpenHelperProvider,
      Provider<AdjustAnalyticsHelper> analyticsHelperProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider,
      Provider<ApplovinAppOpenAdManager> applovinAppOpenAdManagerProvider,
      Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider,
      Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<PreloadingMonitor> preloadingMonitorProvider,
      Provider<DeferredThumbnailPreloadingService> deferredThumbnailPreloadingServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<PerformanceProfiler> performanceProfilerProvider) {
    this.admobHelperProvider = admobHelperProvider;
    this.appOpenHelperProvider = appOpenHelperProvider;
    this.analyticsHelperProvider = analyticsHelperProvider;
    this.analyticsTrackerProvider = analyticsTrackerProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
    this.applovinRewardedAdManagerProvider = applovinRewardedAdManagerProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.applovinBannerAdManagerProvider = applovinBannerAdManagerProvider;
    this.applovinAppOpenAdManagerProvider = applovinAppOpenAdManagerProvider;
    this.coreBatteryServiceHelperProvider = coreBatteryServiceHelperProvider;
    this.chargingOverlayServiceHelperProvider = chargingOverlayServiceHelperProvider;
    this.animationPreloadingRepositoryProvider = animationPreloadingRepositoryProvider;
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.preloadingMonitorProvider = preloadingMonitorProvider;
    this.deferredThumbnailPreloadingServiceProvider = deferredThumbnailPreloadingServiceProvider;
    this.firebaseInitMonitorProvider = firebaseInitMonitorProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
    this.performanceProfilerProvider = performanceProfilerProvider;
  }

  public static MembersInjector<BatteryApplication> create(
      Provider<AdmobHelper> admobHelperProvider, Provider<AppOpenHelper> appOpenHelperProvider,
      Provider<AdjustAnalyticsHelper> analyticsHelperProvider,
      Provider<AnalyticsTracker> analyticsTrackerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<PreferencesHelper> preferencesHelperProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider,
      Provider<ApplovinAppOpenAdManager> applovinAppOpenAdManagerProvider,
      Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider,
      Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider,
      Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<PreloadingMonitor> preloadingMonitorProvider,
      Provider<DeferredThumbnailPreloadingService> deferredThumbnailPreloadingServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<PerformanceProfiler> performanceProfilerProvider) {
    return new BatteryApplication_MembersInjector(admobHelperProvider, appOpenHelperProvider, analyticsHelperProvider, analyticsTrackerProvider, remoteConfigHelperProvider, preferencesHelperProvider, appRepositoryProvider, applovinNativeAdManagerProvider, applovinRewardedAdManagerProvider, applovinInterstitialAdManagerProvider, applovinBannerAdManagerProvider, applovinAppOpenAdManagerProvider, coreBatteryServiceHelperProvider, chargingOverlayServiceHelperProvider, animationPreloadingRepositoryProvider, animationDataServiceProvider, preloadingMonitorProvider, deferredThumbnailPreloadingServiceProvider, firebaseInitMonitorProvider, memoryAnalyzerProvider, performanceProfilerProvider);
  }

  @Override
  public void injectMembers(BatteryApplication instance) {
    AdLibHiltApplication_MembersInjector.injectAdmobHelper(instance, admobHelperProvider.get());
    AdLibHiltApplication_MembersInjector.injectAppOpenHelper(instance, appOpenHelperProvider.get());
    AdLibHiltApplication_MembersInjector.injectAnalyticsHelper(instance, analyticsHelperProvider.get());
    AdLibHiltApplication_MembersInjector.injectAnalyticsTracker(instance, analyticsTrackerProvider.get());
    AdLibHiltApplication_MembersInjector.injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
    injectPreferencesHelper(instance, preferencesHelperProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
    injectApplovinRewardedAdManager(instance, applovinRewardedAdManagerProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectApplovinBannerAdManager(instance, applovinBannerAdManagerProvider.get());
    injectApplovinAppOpenAdManager(instance, applovinAppOpenAdManagerProvider.get());
    injectCoreBatteryServiceHelper(instance, coreBatteryServiceHelperProvider.get());
    injectChargingOverlayServiceHelper(instance, chargingOverlayServiceHelperProvider.get());
    injectAnimationPreloadingRepository(instance, animationPreloadingRepositoryProvider.get());
    injectAnimationDataService(instance, animationDataServiceProvider.get());
    injectPreloadingMonitor(instance, preloadingMonitorProvider.get());
    injectDeferredThumbnailPreloadingService(instance, deferredThumbnailPreloadingServiceProvider.get());
    injectFirebaseInitMonitor(instance, firebaseInitMonitorProvider.get());
    injectMemoryAnalyzer(instance, memoryAnalyzerProvider.get());
    injectPerformanceProfiler(instance, performanceProfilerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.preferencesHelper")
  public static void injectPreferencesHelper(BatteryApplication instance,
      PreferencesHelper preferencesHelper) {
    instance.preferencesHelper = preferencesHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.appRepository")
  public static void injectAppRepository(BatteryApplication instance, AppRepository appRepository) {
    instance.appRepository = appRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(BatteryApplication instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.applovinRewardedAdManager")
  public static void injectApplovinRewardedAdManager(BatteryApplication instance,
      ApplovinRewardedAdManager applovinRewardedAdManager) {
    instance.applovinRewardedAdManager = applovinRewardedAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(BatteryApplication instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.applovinBannerAdManager")
  public static void injectApplovinBannerAdManager(BatteryApplication instance,
      ApplovinBannerAdManager applovinBannerAdManager) {
    instance.applovinBannerAdManager = applovinBannerAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.applovinAppOpenAdManager")
  public static void injectApplovinAppOpenAdManager(BatteryApplication instance,
      ApplovinAppOpenAdManager applovinAppOpenAdManager) {
    instance.applovinAppOpenAdManager = applovinAppOpenAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.coreBatteryServiceHelper")
  public static void injectCoreBatteryServiceHelper(BatteryApplication instance,
      CoreBatteryServiceHelper coreBatteryServiceHelper) {
    instance.coreBatteryServiceHelper = coreBatteryServiceHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.chargingOverlayServiceHelper")
  public static void injectChargingOverlayServiceHelper(BatteryApplication instance,
      ChargingOverlayServiceHelper chargingOverlayServiceHelper) {
    instance.chargingOverlayServiceHelper = chargingOverlayServiceHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.animationPreloadingRepository")
  public static void injectAnimationPreloadingRepository(BatteryApplication instance,
      AnimationPreloadingRepository animationPreloadingRepository) {
    instance.animationPreloadingRepository = animationPreloadingRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.animationDataService")
  public static void injectAnimationDataService(BatteryApplication instance,
      AnimationDataService animationDataService) {
    instance.animationDataService = animationDataService;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.preloadingMonitor")
  public static void injectPreloadingMonitor(BatteryApplication instance,
      PreloadingMonitor preloadingMonitor) {
    instance.preloadingMonitor = preloadingMonitor;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.deferredThumbnailPreloadingService")
  public static void injectDeferredThumbnailPreloadingService(BatteryApplication instance,
      DeferredThumbnailPreloadingService deferredThumbnailPreloadingService) {
    instance.deferredThumbnailPreloadingService = deferredThumbnailPreloadingService;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.firebaseInitMonitor")
  public static void injectFirebaseInitMonitor(BatteryApplication instance,
      FirebaseInitializationMonitor firebaseInitMonitor) {
    instance.firebaseInitMonitor = firebaseInitMonitor;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.memoryAnalyzer")
  public static void injectMemoryAnalyzer(BatteryApplication instance,
      MemoryAnalyzer memoryAnalyzer) {
    instance.memoryAnalyzer = memoryAnalyzer;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.BatteryApplication.performanceProfiler")
  public static void injectPerformanceProfiler(BatteryApplication instance,
      PerformanceProfiler performanceProfiler) {
    instance.performanceProfiler = performanceProfiler;
  }
}
