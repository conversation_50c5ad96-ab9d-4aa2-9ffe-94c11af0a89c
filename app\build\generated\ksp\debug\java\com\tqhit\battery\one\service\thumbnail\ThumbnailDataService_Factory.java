package com.tqhit.battery.one.service.thumbnail;

import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import com.tqhit.battery.one.service.firebase.LocalConfigFallbackService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailDataService_Factory implements Factory<ThumbnailDataService> {
  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider;

  private final Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider;

  public ThumbnailDataService_Factory(Provider<AnimationDataService> animationDataServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider) {
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.firebaseInitMonitorProvider = firebaseInitMonitorProvider;
    this.localConfigFallbackServiceProvider = localConfigFallbackServiceProvider;
  }

  @Override
  public ThumbnailDataService get() {
    return newInstance(animationDataServiceProvider.get(), firebaseInitMonitorProvider.get(), localConfigFallbackServiceProvider.get());
  }

  public static ThumbnailDataService_Factory create(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider) {
    return new ThumbnailDataService_Factory(animationDataServiceProvider, firebaseInitMonitorProvider, localConfigFallbackServiceProvider);
  }

  public static ThumbnailDataService newInstance(AnimationDataService animationDataService,
      FirebaseInitializationMonitor firebaseInitMonitor,
      LocalConfigFallbackService localConfigFallbackService) {
    return new ThumbnailDataService(animationDataService, firebaseInitMonitor, localConfigFallbackService);
  }
}
