package com.tqhit.battery.one.service.optimization;

import com.tqhit.battery.one.manager.quota.StorageQuotaManager;
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager;
import com.tqhit.battery.one.service.thumbnail.MemoryAwareThumbnailPreloader;
import com.tqhit.battery.one.utils.CpuOptimizedPreloader;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import com.tqhit.battery.one.utils.PerformanceProfiler;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MemoryOptimizationService_MembersInjector implements MembersInjector<MemoryOptimizationService> {
  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  private final Provider<StorageQuotaManager> storageQuotaManagerProvider;

  private final Provider<OptimizedStorageManager> optimizedStorageManagerProvider;

  private final Provider<PerformanceProfiler> performanceProfilerProvider;

  private final Provider<CpuOptimizedPreloader> cpuOptimizedPreloaderProvider;

  private final Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider;

  public MemoryOptimizationService_MembersInjector(Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<StorageQuotaManager> storageQuotaManagerProvider,
      Provider<OptimizedStorageManager> optimizedStorageManagerProvider,
      Provider<PerformanceProfiler> performanceProfilerProvider,
      Provider<CpuOptimizedPreloader> cpuOptimizedPreloaderProvider,
      Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider) {
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
    this.storageQuotaManagerProvider = storageQuotaManagerProvider;
    this.optimizedStorageManagerProvider = optimizedStorageManagerProvider;
    this.performanceProfilerProvider = performanceProfilerProvider;
    this.cpuOptimizedPreloaderProvider = cpuOptimizedPreloaderProvider;
    this.memoryAwareThumbnailPreloaderProvider = memoryAwareThumbnailPreloaderProvider;
  }

  public static MembersInjector<MemoryOptimizationService> create(
      Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<StorageQuotaManager> storageQuotaManagerProvider,
      Provider<OptimizedStorageManager> optimizedStorageManagerProvider,
      Provider<PerformanceProfiler> performanceProfilerProvider,
      Provider<CpuOptimizedPreloader> cpuOptimizedPreloaderProvider,
      Provider<MemoryAwareThumbnailPreloader> memoryAwareThumbnailPreloaderProvider) {
    return new MemoryOptimizationService_MembersInjector(memoryAnalyzerProvider, storageQuotaManagerProvider, optimizedStorageManagerProvider, performanceProfilerProvider, cpuOptimizedPreloaderProvider, memoryAwareThumbnailPreloaderProvider);
  }

  @Override
  public void injectMembers(MemoryOptimizationService instance) {
    injectMemoryAnalyzer(instance, memoryAnalyzerProvider.get());
    injectStorageQuotaManager(instance, storageQuotaManagerProvider.get());
    injectOptimizedStorageManager(instance, optimizedStorageManagerProvider.get());
    injectPerformanceProfiler(instance, performanceProfilerProvider.get());
    injectCpuOptimizedPreloader(instance, cpuOptimizedPreloaderProvider.get());
    injectMemoryAwareThumbnailPreloader(instance, memoryAwareThumbnailPreloaderProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.memoryAnalyzer")
  public static void injectMemoryAnalyzer(MemoryOptimizationService instance,
      MemoryAnalyzer memoryAnalyzer) {
    instance.memoryAnalyzer = memoryAnalyzer;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.storageQuotaManager")
  public static void injectStorageQuotaManager(MemoryOptimizationService instance,
      StorageQuotaManager storageQuotaManager) {
    instance.storageQuotaManager = storageQuotaManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.optimizedStorageManager")
  public static void injectOptimizedStorageManager(MemoryOptimizationService instance,
      OptimizedStorageManager optimizedStorageManager) {
    instance.optimizedStorageManager = optimizedStorageManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.performanceProfiler")
  public static void injectPerformanceProfiler(MemoryOptimizationService instance,
      PerformanceProfiler performanceProfiler) {
    instance.performanceProfiler = performanceProfiler;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.cpuOptimizedPreloader")
  public static void injectCpuOptimizedPreloader(MemoryOptimizationService instance,
      CpuOptimizedPreloader cpuOptimizedPreloader) {
    instance.cpuOptimizedPreloader = cpuOptimizedPreloader;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.optimization.MemoryOptimizationService.memoryAwareThumbnailPreloader")
  public static void injectMemoryAwareThumbnailPreloader(MemoryOptimizationService instance,
      MemoryAwareThumbnailPreloader memoryAwareThumbnailPreloader) {
    instance.memoryAwareThumbnailPreloader = memoryAwareThumbnailPreloader;
  }
}
