(com.tqhit.battery.one.BatteryApplication:com.tqhit.battery.one.activity.animation.AnimationActivity2com.tqhit.battery.one.activity.debug.DebugActivity0com.tqhit.battery.one.activity.main.MainActivity>com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity=com.tqhit.battery.one.activity.password.EnterPasswordActivity4com.tqhit.battery.one.activity.splash.SplashActivity8com.tqhit.battery.one.activity.starting.StartingActivity;com.tqhit.battery.one.activity.starting.StartingViewAdapter7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager6com.tqhit.battery.one.ads.core.ApplovinBannerAdManager<com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager6com.tqhit.battery.one.ads.core.ApplovinNativeAdManager8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager<com.tqhit.battery.one.component.progress.VerticalProgressBar;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog>com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog:com.tqhit.battery.one.dialog.language.SelectLanguageDialogBcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog4com.tqhit.battery.one.dialog.theme.SelectColorDialog4com.tqhit.battery.one.dialog.theme.SelectThemeDialog0com.tqhit.battery.one.dialog.utils.LoadingDialog5com.tqhit.battery.one.dialog.utils.NotificationDialogGcom.tqhit.battery.one.features.navigation.AppNavigator.NavigationMethod;com.tqhit.battery.one.features.navigation.StateChangeReasonCcom.tqhit.battery.one.features.navigation.SharedNavigationViewModelUcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapterccom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolderecom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppDiffCallbackTcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogGcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCacheLcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragmentMcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModelScom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepositoryWcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProviderPcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsServiceMcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCacheMcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCacheIcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager>com.tqhit.battery.one.features.stats.discharge.domain.AppStateYcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.NoValidationNeededLcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.ValidPcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.CorrectedDcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionTypeHcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategyMcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragmentNcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModelTcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceDcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCacheEcom.tqhit.battery.one.features.stats.health.data.HealthChartTimeRangeFcom.tqhit.battery.one.features.stats.health.data.HealthCalculationModeHcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModelNcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepositoryTcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService/<EMAIL>@com.tqhit.battery.one.fragment.main.others.adapter.OthersAdapterQcom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersViewHolderScom.tqhit.battery.one.fragment.main.others.adapter.OthersAdapter.OthersDiffCallback6com.tqhit.battery.one.glide.MemoryOptimizedGlideModule9com.tqhit.battery.one.manager.graph.BatteryHistoryManager=<EMAIL>;com.tqhit.battery.one.repository.PreloadingResult.AllFailed7com.tqhit.battery.one.repository.PreloadingResult.Error?<EMAIL>@com.tqhit.battery.one.repository.ThumbnailPreloadingResult.Error4com.tqhit.battery.one.service.ChargingOverlayServicegcom.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor.FirebaseConfigState.NotInitializedecom.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor.FirebaseConfigState.Initializing^com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor.FirebaseConfigState.Ready_com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor.FirebaseConfigState.Failedacom.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor.FirebaseConfigState.TimedOutDcom.tqhit.battery.one.service.optimization.MemoryOptimizationServiceJcom.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingServiceScom.tqhit.battery.one.utils.CpuOptimizedPreloader.PreloadOperation.AnimationPreloadScom.tqhit.battery.one.utils.CpuOptimizedPreloader.PreloadOperation.ThumbnailPreload:com.tqhit.battery.one.utils.CpuOptimizedPreloader.Priority?com.tqhit.battery.one.utils.CpuOptimizedPreloader.CpuUsageLevel4com.tqhit.battery.one.utils.LowPriorityThreadFactory7com.tqhit.battery.one.utils.NormalPriorityThreadFactory5com.tqhit.battery.one.utils.HighPriorityThreadFactory>com.tqhit.battery.one.utils.MemoryAnalyzer.MemoryPressureLevel4com.tqhit.battery.one.utils.MemoryAnalyzer.IssueType3com.tqhit.battery.one.utils.MemoryAnalyzer.SeverityAcom.tqhit.battery.one.utils.PerformanceProfiler.ViolationSeverity,com.tqhit.battery.one.viewmodel.AppViewModel<com.tqhit.battery.one.viewmodel.animation.AnimationViewModel8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      