package com.tqhit.battery.one.service.firebase

import android.content.Context
import com.google.gson.Gson
import com.tqhit.battery.one.R
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for providing local fallback configuration values.
 * Uses remote_config_defaults.xml and local assets as fallback when Firebase Remote Config is unavailable.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles local configuration fallback
 * - Open/Closed: Extensible for different configuration sources
 * - Interface Segregation: Provides specific methods for different config types
 */
@Singleton
class LocalConfigFallbackService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "LocalConfigFallback"
        private const val ANIMATION_JSON_KEY = "animation_json"
    }
    
    private var cachedAnimationCategories: List<AnimationCategory>? = null
    private var cachedConfigValues: Map<String, Any>? = null
    
    /**
     * Gets animation categories from local fallback configuration.
     * Returns cached result if available, otherwise parses from remote_config_defaults.xml.
     */
    suspend fun getFallbackAnimationCategories(): List<AnimationCategory> = withContext(Dispatchers.IO) {
        try {
            // Return cached result if available
            cachedAnimationCategories?.let { cached ->
                BatteryLogger.d(TAG, "FALLBACK_CONFIG: Returning cached animation categories (${cached.size} categories)")
                return@withContext cached
            }
            
            BatteryLogger.d(TAG, "FALLBACK_CONFIG: Loading animation categories from local fallback")
            
            // Try to get from remote_config_defaults.xml first
            val animationJson = getFallbackConfigValue(ANIMATION_JSON_KEY) as? String
            
            if (!animationJson.isNullOrBlank()) {
                val categories = parseAnimationCategories(animationJson)
                if (categories.isNotEmpty()) {
                    cachedAnimationCategories = categories
                    BatteryLogger.d(TAG, "FALLBACK_CONFIG: Successfully loaded ${categories.size} categories from defaults")
                    return@withContext categories
                }
            }
            
            // If remote_config_defaults.xml doesn't have valid data, create minimal fallback
            val fallbackCategories = createMinimalFallbackCategories()
            cachedAnimationCategories = fallbackCategories
            BatteryLogger.w(TAG, "FALLBACK_CONFIG: Using minimal fallback categories (${fallbackCategories.size} categories)")
            
            fallbackCategories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "FALLBACK_CONFIG: Error loading fallback animation categories", e)
            val fallbackCategories = createMinimalFallbackCategories()
            cachedAnimationCategories = fallbackCategories
            fallbackCategories
        }
    }
    
    /**
     * Gets a fallback configuration value by key.
     * Reads from remote_config_defaults.xml resource.
     */
    suspend fun getFallbackConfigValue(key: String): Any? = withContext(Dispatchers.IO) {
        try {
            // Load and cache all config values if not already cached
            if (cachedConfigValues == null) {
                cachedConfigValues = loadConfigValuesFromDefaults()
            }
            
            val value = cachedConfigValues?.get(key)
            BatteryLogger.d(TAG, "FALLBACK_CONFIG: Retrieved fallback value for '$key': $value")
            value
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "FALLBACK_CONFIG: Error getting fallback config value for '$key'", e)
            null
        }
    }
    
    /**
     * Gets a fallback boolean configuration value.
     */
    suspend fun getFallbackBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return try {
            val value = getFallbackConfigValue(key)
            when (value) {
                is Boolean -> value
                is String -> value.toBoolean()
                else -> defaultValue
            }
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "FALLBACK_CONFIG: Error parsing boolean for '$key', using default: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * Gets a fallback long configuration value.
     */
    suspend fun getFallbackLong(key: String, defaultValue: Long = 0L): Long {
        return try {
            val value = getFallbackConfigValue(key)
            when (value) {
                is Long -> value
                is Int -> value.toLong()
                is String -> value.toLong()
                else -> defaultValue
            }
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "FALLBACK_CONFIG: Error parsing long for '$key', using default: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * Gets a fallback string configuration value.
     */
    suspend fun getFallbackString(key: String, defaultValue: String = ""): String {
        return try {
            val value = getFallbackConfigValue(key)
            value?.toString() ?: defaultValue
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "FALLBACK_CONFIG: Error getting string for '$key', using default: $defaultValue")
            defaultValue
        }
    }
    
    /**
     * Checks if fallback configuration is available for critical keys.
     */
    suspend fun isFallbackConfigAvailable(): Boolean = withContext(Dispatchers.IO) {
        try {
            val animationJson = getFallbackString(ANIMATION_JSON_KEY)
            val bannerEnabled = getFallbackBoolean("bn_enable")
            val aoaEnabled = getFallbackBoolean("aoa_enable")
            
            val isAvailable = animationJson.isNotBlank() && animationJson.length > 50
            
            BatteryLogger.d(TAG, "FALLBACK_CONFIG: Availability check - " +
                    "Animation JSON available: ${animationJson.isNotBlank()}, " +
                    "Banner config: $bannerEnabled, " +
                    "AOA config: $aoaEnabled, " +
                    "Overall available: $isAvailable")
            
            isAvailable
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "FALLBACK_CONFIG: Error checking fallback availability", e)
            false
        }
    }
    
    /**
     * Clears cached fallback values to force reload.
     */
    fun clearCache() {
        BatteryLogger.d(TAG, "FALLBACK_CONFIG: Clearing cached fallback values")
        cachedAnimationCategories = null
        cachedConfigValues = null
    }
    
    /**
     * Parses animation categories from JSON string.
     */
    private fun parseAnimationCategories(jsonString: String): List<AnimationCategory> {
        return try {
            val categories = gson.fromJson(jsonString, Array<AnimationCategory>::class.java).toList()
            
            // Validate parsed categories
            val validCategories = categories.filter { category ->
                category.name.isNotBlank() && category.content.isNotEmpty()
            }
            
            if (validCategories.size != categories.size) {
                BatteryLogger.w(TAG, "FALLBACK_CONFIG: Filtered out ${categories.size - validCategories.size} invalid categories")
            }
            
            validCategories
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "FALLBACK_CONFIG: Error parsing animation categories JSON", e)
            emptyList()
        }
    }
    
    /**
     * Loads configuration values from remote_config_defaults.xml.
     */
    private fun loadConfigValuesFromDefaults(): Map<String, Any> {
        return try {
            val configMap = mutableMapOf<String, Any>()
            
            // Parse remote_config_defaults.xml
            val xmlResourceParser = context.resources.getXml(R.xml.remote_config_defaults)
            
            var eventType = xmlResourceParser.eventType
            var currentKey: String? = null
            
            while (eventType != org.xmlpull.v1.XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    org.xmlpull.v1.XmlPullParser.START_TAG -> {
                        when (xmlResourceParser.name) {
                            "key" -> {
                                xmlResourceParser.next()
                                if (xmlResourceParser.eventType == org.xmlpull.v1.XmlPullParser.TEXT) {
                                    currentKey = xmlResourceParser.text
                                }
                            }
                            "value" -> {
                                xmlResourceParser.next()
                                if (xmlResourceParser.eventType == org.xmlpull.v1.XmlPullParser.TEXT && currentKey != null) {
                                    val value = xmlResourceParser.text
                                    configMap[currentKey] = parseConfigValue(value)
                                    currentKey = null
                                }
                            }
                        }
                    }
                }
                eventType = xmlResourceParser.next()
            }
            
            xmlResourceParser.close()
            
            BatteryLogger.d(TAG, "FALLBACK_CONFIG: Loaded ${configMap.size} config values from defaults")
            configMap
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "FALLBACK_CONFIG: Error loading config values from defaults", e)
            emptyMap()
        }
    }
    
    /**
     * Parses a configuration value to appropriate type.
     */
    private fun parseConfigValue(value: String): Any {
        return when {
            value.equals("true", ignoreCase = true) -> true
            value.equals("false", ignoreCase = true) -> false
            value.toLongOrNull() != null -> value.toLong()
            else -> value
        }
    }
    
    /**
     * Creates minimal fallback animation categories for emergency scenarios.
     */
    private fun createMinimalFallbackCategories(): List<AnimationCategory> {
        // This would be a minimal set of categories with basic animations
        // In a real implementation, you might want to include some basic animations
        BatteryLogger.w(TAG, "FALLBACK_CONFIG: Creating minimal fallback categories")
        return emptyList() // For now, return empty list to avoid errors
    }
}
