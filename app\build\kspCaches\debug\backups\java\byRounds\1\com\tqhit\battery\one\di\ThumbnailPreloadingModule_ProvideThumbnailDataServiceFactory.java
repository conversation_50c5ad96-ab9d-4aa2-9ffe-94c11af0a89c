package com.tqhit.battery.one.di;

import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import com.tqhit.battery.one.service.firebase.LocalConfigFallbackService;
import com.tqhit.battery.one.service.thumbnail.ThumbnailDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory implements Factory<ThumbnailDataService> {
  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider;

  private final Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider;

  public ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider) {
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.firebaseInitMonitorProvider = firebaseInitMonitorProvider;
    this.localConfigFallbackServiceProvider = localConfigFallbackServiceProvider;
  }

  @Override
  public ThumbnailDataService get() {
    return provideThumbnailDataService(animationDataServiceProvider.get(), firebaseInitMonitorProvider.get(), localConfigFallbackServiceProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory create(
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider,
      Provider<LocalConfigFallbackService> localConfigFallbackServiceProvider) {
    return new ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory(animationDataServiceProvider, firebaseInitMonitorProvider, localConfigFallbackServiceProvider);
  }

  public static ThumbnailDataService provideThumbnailDataService(
      AnimationDataService animationDataService, FirebaseInitializationMonitor firebaseInitMonitor,
      LocalConfigFallbackService localConfigFallbackService) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideThumbnailDataService(animationDataService, firebaseInitMonitor, localConfigFallbackService));
  }
}
