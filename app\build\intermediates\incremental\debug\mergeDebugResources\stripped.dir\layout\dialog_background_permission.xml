<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerInParent="false">

    <RelativeLayout
        android:background="@drawable/white_block"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:elevation="25dp">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Icon Section -->
            <LinearLayout
                android:gravity="center"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:background="@drawable/ic_health_1"
                android:visibility="visible"
                android:layout_width="105dp"
                android:layout_height="106dp"
                android:layout_marginBottom="16dp"/>

            <!-- Title -->
            <TextView
                android:textSize="22sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/background_permission_dialog_title"
                android:textStyle="bold"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>

            <!-- Message -->
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:id="@+id/dialog_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/background_permission_dialog_message"
                android:textAlignment="gravity"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:justificationMode="inter_word"/>

            <!-- Don't Kill My App Link -->
            <TextView
                android:textSize="16sp"
                android:textColor="?attr/colorr"
                android:gravity="center"
                android:id="@+id/dont_kill_my_app_link"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="@string/background_permission_dont_kill_link"
                android:textStyle="bold"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>

            <!-- Button Container -->
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginTop="8dp">

                <!-- Close Button -->
                <Button
                    android:id="@+id/btn_close"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/white_block"
                    android:text="@string/background_permission_close"
                    android:textColor="?attr/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    style="@style/Widget.AppCompat.Button.Borderless"/>

                <!-- Allow Button -->
                <Button
                    android:id="@+id/btn_allow"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/green_button"
                    android:text="@string/background_permission_allow"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    style="@style/Widget.AppCompat.Button.Borderless"/>

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>
