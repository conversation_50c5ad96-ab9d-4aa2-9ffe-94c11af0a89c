package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.service.thumbnail.MemoryAwareThumbnailPreloader;
import com.tqhit.battery.one.utils.MemoryAnalyzer;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideMemoryAwareThumbnailPreloaderFactory implements Factory<MemoryAwareThumbnailPreloader> {
  private final Provider<Context> contextProvider;

  private final Provider<ThumbnailFileManager> fileManagerProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  public ThumbnailPreloadingModule_ProvideMemoryAwareThumbnailPreloaderFactory(
      Provider<Context> contextProvider, Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    this.contextProvider = contextProvider;
    this.fileManagerProvider = fileManagerProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
  }

  @Override
  public MemoryAwareThumbnailPreloader get() {
    return provideMemoryAwareThumbnailPreloader(contextProvider.get(), fileManagerProvider.get(), memoryAnalyzerProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideMemoryAwareThumbnailPreloaderFactory create(
      Provider<Context> contextProvider, Provider<ThumbnailFileManager> fileManagerProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider) {
    return new ThumbnailPreloadingModule_ProvideMemoryAwareThumbnailPreloaderFactory(contextProvider, fileManagerProvider, memoryAnalyzerProvider);
  }

  public static MemoryAwareThumbnailPreloader provideMemoryAwareThumbnailPreloader(Context context,
      ThumbnailFileManager fileManager, MemoryAnalyzer memoryAnalyzer) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideMemoryAwareThumbnailPreloader(context, fileManager, memoryAnalyzer));
  }
}
