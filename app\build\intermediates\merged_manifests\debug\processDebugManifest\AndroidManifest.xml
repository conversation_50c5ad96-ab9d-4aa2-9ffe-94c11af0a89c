<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
    android:versionCode="43"
    android:versionName="1.2.0.20250624" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />

    <permission
        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
        android:protectionLevel="signature" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <queries>
        <intent>
            <action android:name="com.attribution.REFERRAL_PROVIDER" />
        </intent>
        <intent>
            <action android:name="androidx.browser.customtabs.CustomTabsService" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="market" />
        </intent>

        <package android:name="com.android.chrome" />
        <package android:name="com.google.android.webview" />
        <package android:name="com.android.webview" />
        <package android:name="com.android.vending" /> <!-- End of browser content -->
        <!-- For CustomTabsService -->
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent> <!-- End of CustomTabsService -->
        <!-- For MRAID capabilities -->
        <intent>
            <action android:name="android.intent.action.INSERT" />

            <data android:mimeType="vnd.android.cursor.dir/event" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="sms" />
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />

            <data android:path="tel:" />
        </intent>
        <intent>
            <action android:name="com.applovin.am.intent.action.APPHUB_SERVICE" />
        </intent>
        <intent>
            <action android:name="android.intent.action.ACTION_VIEW" />

            <data android:scheme="https" />
        </intent>

        <package android:name="com.facebook.katana" />

        <intent>
            <action android:name="com.digitalturbine.ignite.cl.IgniteRemoteService" />
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <uses-permission android:name="com.applovin.array.apphub.permission.BIND_APPHUB_SERVICE" />

    <permission
        android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.tqhit.battery.one.BatteryApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/Theme.BatteryOne" >
        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-9844172086883515~3386117176" />

        <activity
            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.BatteryOne.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.BatteryOne" />
        <activity
            android:name="com.tqhit.battery.one.activity.main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
            android:configChanges="orientation|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
        <activity
            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
        <activity
            android:name="com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="test_discharge"
                    android:scheme="battery" />
            </intent-filter>
        </activity> <!-- Legacy TestNewChargeActivity removed - was in legacy directory -->
        <!-- DebugActivity will be conditionally included via build variant manifests -->
        <service
            android:name="com.tqhit.battery.one.service.BatteryMonitorService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />
        <service
            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" /> <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
        <service
            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />
        <service
            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />
        <service
            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />
        <service
            android:name="com.tqhit.battery.one.service.optimization.MemoryOptimizationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" /> <!-- HYPR Activities -->
        <activity
            android:name="com.hyprmx.android.sdk.activity.HyprMXOfferViewerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:hardwareAccelerated="true"
            android:label="HyprMX SDK"
            android:launchMode="singleTop"
            android:theme="@style/hyprmx_ActivityTheme" />
        <activity
            android:name="com.hyprmx.android.sdk.activity.HyprMXRequiredInformationActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:theme="@style/hyprmx_RequiredInfoTheme" />
        <activity
            android:name="com.hyprmx.android.sdk.activity.HyprMXNoOffersActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:theme="@style/hyprmx_ActivityTheme" />
        <activity
            android:name="com.hyprmx.android.sdk.overlay.HyprMXBrowserActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:hardwareAccelerated="true"
            android:theme="@style/hyprmx_ActivityTheme" /> <!-- XENOSS core -->
        <!-- android:initOrder="-**********" Try to initialize after all the rest of content providers -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.androidx-startup"
            android:exported="false"
            android:initOrder="-**********" >
            <meta-data
                android:name="com.moloco.sdk.internal.android_context.StartupComponentInitialization"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="com.unity3d.services.core.configuration.AdsSdkInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider> <!-- XENOSS renderer -->
        <activity
            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.templates.renderer.fullscreen.FullscreenWebviewActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:exported="false"
            android:theme="@style/FullscreenAdActivity" >
        </activity>
        <activity
            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.vast.VastActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:exported="false"
            android:screenOrientation="fullSensor"
            android:theme="@style/FullscreenAdActivity" />
        <activity
            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.staticrenderer.StaticAdActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:exported="false"
            android:screenOrientation="fullSensor"
            android:theme="@style/FullscreenAdActivity" />
        <activity
            android:name="com.moloco.sdk.xenoss.sdkdevkit.android.adrenderer.internal.mraid.MraidActivity"
            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
            android:exported="false"
            android:screenOrientation="fullSensor"
            android:theme="@style/FullscreenAdActivity" />
        <activity
            android:name="cat.ereza.customactivityoncrash.activity.DefaultErrorActivity"
            android:process=":error_activity" />

        <provider
            android:name="cat.ereza.customactivityoncrash.provider.CaocInitProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.customactivityoncrashinitprovider"
            android:exported="false"
            android:initOrder="101" />

        <activity
            android:name="com.yandex.mobile.ads.common.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:enableOnBackInvokedCallback="false"
            android:theme="@style/MonetizationAdsInternal.AdActivity" />

        <provider
            android:name="com.yandex.mobile.ads.core.initializer.MobileAdsInitializeProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.MobileAdsInitializeProvider"
            android:exported="false" />

        <activity
            android:name="com.yandex.mobile.ads.features.debugpanel.ui.IntegrationInspectorActivity"
            android:enableOnBackInvokedCallback="false"
            android:exported="false"
            android:theme="@style/DebugPanelTheme" />

        <provider
            android:name="com.yandex.mobile.ads.features.debugpanel.data.local.DebugPanelFileProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.monetization.ads.inspector.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/debug_panel_file_paths" />
        </provider>

        <activity
            android:name="com.unity3d.services.ads.adunit.AdUnitActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.unity3d.services.ads.adunit.AdUnitTransparentSoftwareActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
            android:exported="false"
            android:hardwareAccelerated="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.unity3d.services.ads.adunit.AdUnitSoftwareActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
            android:exported="false"
            android:hardwareAccelerated="false"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.unity3d.ads.adplayer.FullScreenWebViewDisplay"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|mnc|mcc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|uiMode|touchscreen"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.inmobi.ads.rendering.InMobiAdActivity"
            android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout|locale|fontScale|uiMode"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar" />

        <meta-data
            android:name="com.bytedance.sdk.pangle.version"
            android:value="7.2.0.6" /> <!-- 下面的activity和service必须注册 -->
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTCeilingLandingPageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTLandingPageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_landing_page" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTPlayableLandingPageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_landing_page" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTVideoLandingPageLink2Activity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_landing_page" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTDelegateActivity"
            android:launchMode="standard"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTWebsiteActivity"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:theme="@style/tt_privacy_landing_page" />

        <service android:name="com.bytedance.sdk.openadsdk.multipro.aidl.BinderPoolService" />

        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTAppOpenAdActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_app_open_ad_no_animation" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_new" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTRewardExpressVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_new" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_new" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTFullScreenExpressVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_new" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_interaction" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTInterstitialExpressActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_interaction" />
        <activity
            android:name="com.bytedance.sdk.openadsdk.activity.TTAdActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="standard"
            android:theme="@style/tt_full_screen_new" />

        <provider
            android:name="io.bidmachine.BidMachineInitProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.bidmachineinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <activity
            android:name="io.bidmachine.nativead.view.VideoPlayerActivity"
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
        <activity
            android:name="io.bidmachine.rendering.ad.fullscreen.FullScreenActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="io.bidmachine.iab.mraid.MraidActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="io.bidmachine.iab.vast.activity.VastActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.mobileadsinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="com.google.android.gms.ads.AdService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false" />
        <activity
            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.applovin.sdk.AppLovinInitProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.applovininitprovider"
            android:exported="false"
            android:initOrder="101" /> <!-- Init order is 101 so we're before Firebase/Google which uses 100 -->
        <activity
            android:name="com.applovin.adview.AppLovinFullscreenActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.applovin.sdk.AppLovinWebViewActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" /> <!-- Mediation Debugger Activities -->
        <activity
            android:name="com.applovin.mediation.MaxDebuggerActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerDetailActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerMultiAdActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerAdUnitsListActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerAdUnitWaterfallsListActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerAdUnitDetailActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerCmpNetworksListActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerTcfConsentStatusesListActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerTcfInfoListActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerTcfStringActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerTestLiveNetworkActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerTestModeNetworkActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerUnifiedFlowActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.mediation.MaxDebuggerWaterfallSegmentsActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.mediation.MaxDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.creative.MaxCreativeDebuggerActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" />
        <activity
            android:name="com.applovin.creative.MaxCreativeDebuggerDisplayedAdActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:theme="@style/com.applovin.creative.CreativeDebuggerActivity.Theme" /> <!-- Services -->
        <service
            android:name="com.applovin.impl.adview.activity.FullscreenAdService"
            android:exported="false"
            android:stopWithTask="false" />

        <activity
            android:name="com.chartboost.sdk.view.CBImpressionActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:enableOnBackInvokedCallback="true"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> <!-- ExoPlayer DownloadService -->
        <service
            android:name="com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService"
            android:exported="false" >

            <!-- This is needed for Scheduler -->
            <intent-filter>
                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service> <!-- AppMetrica Analytics: common service -->
        <service
            android:name="io.appmetrica.analytics.internal.AppMetricaService"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="io.appmetrica.analytics.IAppMetricaService" />

                <data android:scheme="appmetrica" />
            </intent-filter>
        </service> <!-- To track preinstallations -->
        <provider
            android:name="io.appmetrica.analytics.internal.PreloadInfoContentProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.appmetrica.preloadinfo.retail"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name="com.google.firebase.sessions.SessionLifecycleService"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <activity
            android:name="com.facebook.ads.AudienceNetworkActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.facebook.ads.AudienceNetworkContentProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.AudienceNetworkContentProvider"
            android:exported="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <activity
            android:name="com.vungle.ads.internal.ui.VungleActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop" />

        <provider
            android:name="com.vungle.ads.VungleProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.vungle-provider"
            android:exported="false"
            android:initOrder="102" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <provider
            android:name="com.squareup.picasso.PicassoProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.com.squareup.picasso"
            android:exported="false" />

        <activity
            android:name="com.ironsource.sdk.controller.ControllerActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar" />
        <activity
            android:name="com.ironsource.sdk.controller.InterstitialActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.ironsource.sdk.controller.OpenUrlActivity"
            android:configChanges="orientation|screenSize"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.ironsource.mediationsdk.testSuite.TestSuiteActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar" >
            <meta-data
                android:name="android.webkit.WebView.EnableSafeBrowsing"
                android:value="true" />
        </activity>

        <provider
            android:name="com.ironsource.lifecycle.IronsourceLifecycleProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.IronsourceLifecycleProvider"
            android:exported="false" />
        <provider
            android:name="com.ironsource.lifecycle.LevelPlayActivityLifecycleProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.LevelPlayActivityLifecycleProvider"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <provider
            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.adjust-lifecycle-provider"
            android:exported="false" />

        <activity
            android:name="sg.bigo.ads.ad.splash.AdSplashActivity"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme" />
        <activity
            android:name="sg.bigo.ads.ad.splash.LandscapeAdSplashActivity"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme" />

        <provider
            android:name="sg.bigo.ads.controller.provider.BigoAdsProvider"
            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.BigoAdsProvider"
            android:exported="false" />

        <activity
            android:name="sg.bigo.ads.controller.form.AdFormActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="sg.bigo.ads.api.AdActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.api.PopupAdActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentDialog"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.api.LandingStyleableActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.Holo.Light.Dialog.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.api.LandscapeAdActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.api.CompanionAdActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.api.LandscapeCompanionAdActivity"
            android:configChanges="orientation|screenSize"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.Holo.Light.NoActionBar"
            android:windowSoftInputMode="stateAlwaysHidden" >
        </activity>
        <activity
            android:name="sg.bigo.ads.core.mraid.MraidVideoActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.fyber.inneractive.sdk.activities.InneractiveInternalBrowserActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:screenOrientation="fullUser" />
        <activity
            android:name="com.fyber.inneractive.sdk.activities.InneractiveFullscreenAdActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.fyber.inneractive.sdk.activities.InneractiveRichMediaVideoPlayerActivityCore"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.fyber.inneractive.sdk.activities.InternalStoreWebpageActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="true"
            android:screenOrientation="sensor"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.fyber.inneractive.sdk.activities.FyberReportAdActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:screenOrientation="fullUser" /> <!-- mbridge base activity -->
        <activity
            android:name="com.mbridge.msdk.activity.MBCommonActivity"
            android:configChanges="keyboard|orientation"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@style/mbridge_transparent_common_activity_style" /> <!-- integration rewardVideo if aggregation nativeX pls add start -->
        <activity android:name="com.mbridge.msdk.out.LoadingActivity" /> <!-- integration rewardVideo if aggregation nativeX pls add end -->
        <activity
            android:name="com.mbridge.msdk.newreward.player.MBRewardVideoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:excludeFromRecents="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.mbridge.msdk.reward.player.MBRewardVideoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:excludeFromRecents="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name="com.mbridge.msdk.foundation.same.broadcast.NetWorkChangeReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
    </application>

</manifest>