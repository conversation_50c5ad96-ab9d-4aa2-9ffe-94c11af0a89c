package com.tqhit.battery.one.di;

import android.content.Context;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.manager.quota.StorageQuotaManager;
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideStorageQuotaManagerFactory implements Factory<StorageQuotaManager> {
  private final Provider<Context> contextProvider;

  private final Provider<OptimizedStorageManager> storageManagerProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public ThumbnailPreloadingModule_ProvideStorageQuotaManagerFactory(
      Provider<Context> contextProvider, Provider<OptimizedStorageManager> storageManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    this.contextProvider = contextProvider;
    this.storageManagerProvider = storageManagerProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public StorageQuotaManager get() {
    return provideStorageQuotaManager(contextProvider.get(), storageManagerProvider.get(), preferencesHelperProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideStorageQuotaManagerFactory create(
      Provider<Context> contextProvider, Provider<OptimizedStorageManager> storageManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new ThumbnailPreloadingModule_ProvideStorageQuotaManagerFactory(contextProvider, storageManagerProvider, preferencesHelperProvider);
  }

  public static StorageQuotaManager provideStorageQuotaManager(Context context,
      OptimizedStorageManager storageManager, PreferencesHelper preferencesHelper) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideStorageQuotaManager(context, storageManager, preferencesHelper));
  }
}
