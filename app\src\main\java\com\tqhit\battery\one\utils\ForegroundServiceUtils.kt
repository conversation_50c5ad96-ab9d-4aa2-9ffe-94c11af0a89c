package com.tqhit.battery.one.utils

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Utility class for handling foreground service requirements and permissions.
 * Provides centralized logic for checking and requesting permissions needed
 * for foreground services on different Android API levels.
 */
object ForegroundServiceUtils {
    
    private const val TAG = "ForegroundServiceUtils"
    
    // Request code for notification permission
    const val REQUEST_CODE_NOTIFICATION_PERMISSION = 1001
    
    /**
     * Checks if all required permissions for foreground services are granted.
     * 
     * @param context The application context
     * @return true if all required permissions are granted
     */
    fun hasRequiredPermissions(context: Context): Boolean {
        return hasNotificationPermission(context) && hasForegroundServicePermission(context)
    }
    
    /**
     * Checks if the POST_NOTIFICATIONS permission is granted (Android 13+).
     * 
     * @param context The application context
     * @return true if permission is granted or not required for this API level
     */
    fun hasNotificationPermission(context: Context): Bo<PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, 
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Permission not required for API < 33
        }
    }
    
    /**
     * Checks if the FOREGROUND_SERVICE permission is granted.
     * 
     * @param context The application context
     * @return true if permission is granted
     */
    fun hasForegroundServicePermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context, 
            android.Manifest.permission.FOREGROUND_SERVICE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Requests notification permission if needed and not already granted.
     * 
     * @param activity The activity to request permission from
     * @return true if permission was requested, false if not needed or already granted
     */
    fun requestNotificationPermissionIfNeeded(activity: Activity): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission(activity)) {
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                    REQUEST_CODE_NOTIFICATION_PERMISSION
                )
                return true
            }
        }
        return false
    }
    
    /**
     * Checks if the app can start foreground services based on current Android restrictions.
     * This includes checking for user-visible activity requirements on Android 12+.
     * 
     * @param context The application context
     * @return true if foreground service can likely be started
     */
    fun canStartForegroundService(context: Context): Boolean {
        // Check basic permissions first
        if (!hasRequiredPermissions(context)) {
            BatteryLogger.w(TAG, "Missing required permissions for foreground service")
            return false
        }
        
        // For Android 12+ (API 31+), additional restrictions apply
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Note: We can't easily check if app is in foreground from a service context
            // The service itself will need to handle ForegroundServiceStartNotAllowedException
            BatteryLogger.d(TAG, "Android 12+ detected - foreground service restrictions may apply")
        }
        
        return true
    }
    
    /**
     * Gets a detailed status report of foreground service requirements and permissions.
     * 
     * @param context The application context
     * @return Map containing detailed permission and requirement status
     */
    fun getPermissionStatus(context: Context): Map<String, Any> {
        return mapOf(
            "androidApiLevel" to Build.VERSION.SDK_INT,
            "hasNotificationPermission" to hasNotificationPermission(context),
            "hasForegroundServicePermission" to hasForegroundServicePermission(context),
            "hasAllRequiredPermissions" to hasRequiredPermissions(context),
            "canStartForegroundService" to canStartForegroundService(context),
            "notificationPermissionRequired" to (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU),
            "foregroundServiceRestrictionsApply" to (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
        )
    }
    
    /**
     * Handles the result of a permission request.
     * 
     * @param requestCode The request code from the permission request
     * @param permissions The requested permissions
     * @param grantResults The grant results for the corresponding permissions
     * @return true if the notification permission was granted
     */
    fun handlePermissionResult(
        requestCode: Int, 
        permissions: Array<out String>, 
        grantResults: IntArray
    ): Boolean {
        if (requestCode == REQUEST_CODE_NOTIFICATION_PERMISSION) {
            val notificationPermissionIndex = permissions.indexOf(android.Manifest.permission.POST_NOTIFICATIONS)
            if (notificationPermissionIndex >= 0) {
                val granted = grantResults[notificationPermissionIndex] == PackageManager.PERMISSION_GRANTED
                BatteryLogger.d(TAG, "Notification permission result: ${if (granted) "granted" else "denied"}")
                return granted
            }
        }
        return false
    }
    
    /**
     * Logs the current permission status for debugging purposes.
     * 
     * @param context The application context
     */
    fun logPermissionStatus(context: Context) {
        val status = getPermissionStatus(context)
        BatteryLogger.d(TAG, "Foreground service permission status:")
        status.forEach { (key, value) ->
            BatteryLogger.d(TAG, "  $key: $value")
        }
    }
}
