package com.tqhit.battery.one.utils;

import android.content.Context;
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PerformanceProfiler_Factory implements Factory<PerformanceProfiler> {
  private final Provider<Context> contextProvider;

  private final Provider<MemoryAnalyzer> memoryAnalyzerProvider;

  private final Provider<OptimizedStorageManager> storageManagerProvider;

  public PerformanceProfiler_Factory(Provider<Context> contextProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<OptimizedStorageManager> storageManagerProvider) {
    this.contextProvider = contextProvider;
    this.memoryAnalyzerProvider = memoryAnalyzerProvider;
    this.storageManagerProvider = storageManagerProvider;
  }

  @Override
  public PerformanceProfiler get() {
    return newInstance(contextProvider.get(), memoryAnalyzerProvider.get(), storageManagerProvider.get());
  }

  public static PerformanceProfiler_Factory create(Provider<Context> contextProvider,
      Provider<MemoryAnalyzer> memoryAnalyzerProvider,
      Provider<OptimizedStorageManager> storageManagerProvider) {
    return new PerformanceProfiler_Factory(contextProvider, memoryAnalyzerProvider, storageManagerProvider);
  }

  public static PerformanceProfiler newInstance(Context context, MemoryAnalyzer memoryAnalyzer,
      OptimizedStorageManager storageManager) {
    return new PerformanceProfiler(context, memoryAnalyzer, storageManager);
  }
}
