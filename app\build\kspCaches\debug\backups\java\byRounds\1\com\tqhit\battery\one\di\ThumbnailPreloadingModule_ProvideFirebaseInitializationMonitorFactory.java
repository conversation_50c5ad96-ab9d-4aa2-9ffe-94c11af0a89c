package com.tqhit.battery.one.di;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailPreloadingModule_ProvideFirebaseInitializationMonitorFactory implements Factory<FirebaseInitializationMonitor> {
  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  public ThumbnailPreloadingModule_ProvideFirebaseInitializationMonitorFactory(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
  }

  @Override
  public FirebaseInitializationMonitor get() {
    return provideFirebaseInitializationMonitor(remoteConfigHelperProvider.get());
  }

  public static ThumbnailPreloadingModule_ProvideFirebaseInitializationMonitorFactory create(
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    return new ThumbnailPreloadingModule_ProvideFirebaseInitializationMonitorFactory(remoteConfigHelperProvider);
  }

  public static FirebaseInitializationMonitor provideFirebaseInitializationMonitor(
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    return Preconditions.checkNotNullFromProvides(ThumbnailPreloadingModule.INSTANCE.provideFirebaseInitializationMonitor(remoteConfigHelper));
  }
}
