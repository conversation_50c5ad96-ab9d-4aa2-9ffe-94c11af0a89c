   ( c o m / t q h i t / b a t t e r y / o n e / B a t t e r y A p p l i c a t i o n   ) c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y L o g g e r   2 c o m / t q h i t / b a t t e r y / o n e / B a t t e r y A p p l i c a t i o n $ C o m p a n i o n   : c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / a n i m a t i o n / A n i m a t i o n A c t i v i t y   2 c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / d e b u g / D e b u g A c t i v i t y   0 c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / M a i n A c t i v i t y   : c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / M a i n A c t i v i t y $ C o m p a n i o n   E c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / F r a g m e n t L i f e c y c l e M a n a g e r   O c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / F r a g m e n t L i f e c y c l e M a n a g e r $ C o m p a n i o n   > c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / N a v i g a t i o n H a n d l e r   H c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / N a v i g a t i o n H a n d l e r $ C o m p a n i o n   ; c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / S e r v i c e M a n a g e r   E c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / S e r v i c e M a n a g e r $ C o m p a n i o n   > c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / o v e r l a y / C h a r g i n g O v e r l a y A c t i v i t y   = c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / p a s s w o r d / E n t e r P a s s w o r d A c t i v i t y   G c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / p a s s w o r d / E n t e r P a s s w o r d A c t i v i t y $ C o m p a n i o n   4 c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s p l a s h / S p l a s h A c t i v i t y   > c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s p l a s h / S p l a s h A c t i v i t y $ C o m p a n i o n   8 c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s t a r t i n g / S t a r t i n g A c t i v i t y   ; c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s t a r t i n g / S t a r t i n g V i e w A d a p t e r   7 c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n A p p O p e n A d M a n a g e r   6 c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n B a n n e r A d M a n a g e r   < c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n I n t e r s t i t i a l A d M a n a g e r   6 c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n N a t i v e A d M a n a g e r   8 c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n R e w a r d e d A d M a n a g e r   < c o m / t q h i t / b a t t e r y / o n e / c o m p o n e n t / p r o g r e s s / V e r t i c a l P r o g r e s s B a r   2 c o m / t q h i t / b a t t e r y / o n e / d i / T h u m b n a i l P r e l o a d i n g M o d u l e   ; c o m / t q h i t / b a t t e r y / o n e / d i a l o g / a l a r m / S e l e c t B a t t e r y A l a r m D i a l o g   > c o m / t q h i t / b a t t e r y / o n e / d i a l o g / a l a r m / S e l e c t B a t t e r y A l a r m L o w D i a l o g   : c o m / t q h i t / b a t t e r y / o n e / d i a l o g / c a p a c i t y / C h a n g e C a p a c i t y D i a l o g   9 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / c a p a c i t y / S e t u p P a s s w o r d D i a l o g   : c o m / t q h i t / b a t t e r y / o n e / d i a l o g / l a n g u a g e / S e l e c t L a n g u a g e D i a l o g   B c o m / t q h i t / b a t t e r y / o n e / d i a l o g / p e r m i s s i o n / B a c k g r o u n d P e r m i s s i o n D i a l o g   L c o m / t q h i t / b a t t e r y / o n e / d i a l o g / p e r m i s s i o n / B a c k g r o u n d P e r m i s s i o n D i a l o g $ C o m p a n i o n   4 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / t h e m e / S e l e c t C o l o r D i a l o g   4 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / t h e m e / S e l e c t T h e m e D i a l o g   7 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / A p p l o v i n A d E n t r y P o i n t   0 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / L o a d i n g D i a l o g   5 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / N o t i f i c a t i o n D i a l o g   ? c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / N o t i f i c a t i o n D i a l o g $ C o m p a n i o n   6 c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / P o s t A n i m a t i o n D i a l o g   @ c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / P o s t A n i m a t i o n D i a l o g $ C o m p a n i o n   6 c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / A p p N a v i g a t o r   @ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / A p p N a v i g a t o r $ C o m p a n i o n   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / A p p N a v i g a t o r $ N a v i g a t i o n M e t h o d   B c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / D y n a m i c N a v i g a t i o n M a n a g e r   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / D y n a m i c N a v i g a t i o n M a n a g e r $ C o m p a n i o n   9 c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / N a v i g a t i o n S t a t e   C c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / N a v i g a t i o n S t a t e $ C o m p a n i o n   ? c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / N a v i g a t i o n S t a t e C h a n g e   ; c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / S t a t e C h a n g e R e a s o n   C c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / S h a r e d N a v i g a t i o n V i e w M o d e l   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / S h a r e d N a v i g a t i o n V i e w M o d e l $ C o m p a n i o n   < c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / F r a g m e n t T r a n s i t i o n   ? c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / d i / N a v i g a t i o n D I M o d u l e   J c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / d a t a / A p p P o w e r C o n s u m p t i o n D a t a   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / d a t a / A p p P o w e r C o n s u m p t i o n S u m m a r y   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p e r m i s s i o n / U s a g e S t a t s P e r m i s s i o n M a n a g e r   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p e r m i s s i o n / U s a g e S t a t s P e r m i s s i o n M a n a g e r $ C o m p a n i o n   U c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n A d a p t e r   c c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n A d a p t e r $ A p p V i e w H o l d e r   e c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n A d a p t e r $ A p p D i f f C a l l b a c k   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n D i a l o g   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n D i a l o g $ C o m p a n i o n   [ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n D i a l o g F a c t o r y   P c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / r e p o s i t o r y / A p p U s a g e S t a t s R e p o s i t o r y   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / r e p o s i t o r y / A p p U s a g e S t a t s R e p o s i t o r y $ C o m p a n i o n   B c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / c a c h e / S t a t s C h a r g e C a c h e   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / c a c h e / P r e f s S t a t s C h a r g e C a c h e   Q c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / c a c h e / P r e f s S t a t s C h a r g e C a c h e $ C o m p a n i o n   C c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S e s s i o n   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S e s s i o n $ C o m p a n i o n   B c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S t a t u s   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S t a t u s $ C o m p a n i o n   B c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d i / S t a t s C h a r g e D I M o d u l e   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d o m a i n / C a l c u l a t e S i m p l e C h a r g e E s t i m a t e U s e C a s e   a c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d o m a i n / C a l c u l a t e S i m p l e C h a r g e E s t i m a t e U s e C a s e $ C o m p a n i o n   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e F r a g m e n t   V c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e F r a g m e n t $ C o m p a n i o n   K c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e U i S t a t e   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e V i e w M o d e l   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e V i e w M o d e l $ C o m p a n i o n   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / r e p o s i t o r y / S t a t s C h a r g e R e p o s i t o r y   S c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / r e p o s i t o r y / D e f a u l t S t a t s C h a r g e R e p o s i t o r y   ] c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / r e p o s i t o r y / D e f a u l t S t a t s C h a r g e R e p o s i t o r y $ C o m p a n i o n   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d a t a / C o r e B a t t e r y S t a t u s   Q c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d a t a / C o r e B a t t e r y S t a t u s $ C o m p a n i o n   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d i / C o r e B a t t e r y D I M o d u l e   P c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d o m a i n / C o r e B a t t e r y S t a t s P r o v i d e r   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d o m a i n / D e f a u l t C o r e B a t t e r y S t a t s P r o v i d e r   a c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d o m a i n / D e f a u l t C o r e B a t t e r y S t a t s P r o v i d e r $ C o m p a n i o n   Q c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S e r v i c e H e l p e r   [ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S e r v i c e H e l p e r $ C o m p a n i o n   P c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S t a t s S e r v i c e   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S t a t s S e r v i c e $ C o m p a n i o n   H c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / C u r r e n t S e s s i o n C a c h e   H c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / D i s c h a r g e R a t e s C a c h e   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s C u r r e n t S e s s i o n C a c h e   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s C u r r e n t S e s s i o n C a c h e $ C o m p a n i o n   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s D i s c h a r g e R a t e s C a c h e   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s D i s c h a r g e R a t e s C a c h e $ C o m p a n i o n   H c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a / D i s c h a r g e S e s s i o n D a t a   J c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a / S c r e e n S t a t e C h a n g e E v e n t   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a s o u r c e / S c r e e n S t a t e R e c e i v e r   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a s o u r c e / S c r e e n S t a t e R e c e i v e r $ C o m p a n i o n   F c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d i / S t a t s D i s c h a r g e M o d u l e   O c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d i / S t a t s D i s c h a r g e P r o v i d e r s M o d u l e   I c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / A p p L i f e c y c l e M a n a g e r   S c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / A p p L i f e c y c l e M a n a g e r $ C o m p a n i o n   > c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / A p p S t a t e   I c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e C a l c u l a t o r   S c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e C a l c u l a t o r $ C o m p a n i o n   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e R a t e C a l c u l a t o r   ` c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e R a t e C a l c u l a t o r $ C o n s u m p t i o n B y S t a t e   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e R a t e C a l c u l a t o r $ S e s s i o n R a t e s   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / F u l l S e s s i o n R e E s t i m a t o r   V c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / F u l l S e s s i o n R e E s t i m a t o r $ C o m p a n i o n   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / G a p E s t i m a t i o n C a l c u l a t o r   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / G a p E s t i m a t i o n C a l c u l a t o r $ C o m p a n i o n   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n S t a t e T i m e T r a c k e r   V c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n S t a t e T i m e T r a c k e r $ C o m p a n i o n   J c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e C a l c u l a t o r   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e C a l c u l a t o r $ B a t t e r y R a t e s   V c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e C a l c u l a t o r $ S c r e e n T i m e s   [ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e C a l c u l a t o r $ S c r e e n T i m e D e l t a s   Q c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e V a l i d a t i o n S e r v i c e   [ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e V a l i d a t i o n S e r v i c e $ C o m p a n i o n   F c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / V a l i d a t i o n R e s u l t   Y c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / V a l i d a t i o n R e s u l t $ N o V a l i d a t i o n N e e d e d   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / V a l i d a t i o n R e s u l t $ V a l i d   P c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / V a l i d a t i o n R e s u l t $ C o r r e c t e d   D c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / C o r r e c t i o n T y p e   H c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / C o r r e c t i o n S t r a t e g y   D c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S e s s i o n M a n a g e r   N c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S e s s i o n M e t r i c s C a l c u l a t o r   ] c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S e s s i o n M e t r i c s C a l c u l a t o r $ S e s s i o n M e t r i c s   C c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / T i m e C o n v e r t e r   K c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / A n i m a t i o n H e l p e r   U c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / A n i m a t i o n H e l p e r $ C o m p a n i o n   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e F r a g m e n t   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e F r a g m e n t $ C o m p a n i o n   N c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e U i U p d a t e r   X c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e U i U p d a t e r $ C o m p a n i o n   L c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e U i S t a t e   N c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e V i e w M o d e l   X c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e V i e w M o d e l $ C o m p a n i o n   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e V i e w M o d e l $ T i m e E s t i m a t i o n s   M c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / I n f o B u t t o n M a n a g e r   W c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / I n f o B u t t o n M a n a g e r $ C o m p a n i o n   K c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y   U c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y $ C o m p a n i o n   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / D i s c h a r g e S e s s i o n R e p o s i t o r y   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / D i s c h a r g e S e s s i o n R e p o s i t o r y $ C o m p a n i o n   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e $ C o m p a n i o n   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e H e l p e r   d c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e H e l p e r $ C o m p a n i o n   = c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / c a c h e / H e a l t h C a c h e   D c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / c a c h e / D e f a u l t H e a l t h C a c h e   N c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / c a c h e / D e f a u l t H e a l t h C a c h e $ C o m p a n i o n   ? c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / c a c h e / H e a l t h C a c h e K t   @ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C h a r t D a t a   J c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C h a r t D a t a $ C o m p a n i o n   E c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C h a r t T i m e R a n g e   O c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C h a r t T i m e R a n g e $ C o m p a n i o n   = c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h S t a t u s   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h S t a t u s $ C o m p a n i o n   F c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C a l c u l a t i o n M o d e   = c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d i / H e a l t h D I M o d u l e   P c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / C a l c u l a t e B a t t e r y H e a l t h U s e C a s e   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / C a l c u l a t e B a t t e r y H e a l t h U s e C a s e $ C o m p a n i o n   J c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / G e t H e a l t h H i s t o r y U s e C a s e   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / G e t H e a l t h H i s t o r y U s e C a s e $ C o m p a n i o n   F c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / p r e s e n t a t i o n / H e a l t h U i S t a t e   H c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / p r e s e n t a t i o n / H e a l t h V i e w M o d e l   R c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / p r e s e n t a t i o n / H e a l t h V i e w M o d e l $ C o m p a n i o n   G c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / H e a l t h R e p o s i t o r y   N c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / D e f a u l t H e a l t h R e p o s i t o r y   X c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / D e f a u l t H e a l t h R e p o s i t o r y $ C o m p a n i o n   O c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / H i s t o r y B a t t e r y R e p o s i t o r y   Y c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / H i s t o r y B a t t e r y R e p o s i t o r y $ C o m p a n i o n   T c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e   ^ c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e $ C o m p a n i o n   Z c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e H e l p e r   d c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e H e l p e r $ C o m p a n i o n   / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / H i s t o r y T y p e   2 c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / H e a l t h F r a g m e n t   4 c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / S e t t i n g s F r a g m e n t   > c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / S e t t i n g s F r a g m e n t $ C o m p a n i o n   C c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / A n i m a t i o n G r i d F r a g m e n t   M c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / A n i m a t i o n G r i d F r a g m e n t $ C o m p a n i o n   F c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / A n i m a t i o n A d a p t e r   I c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / A n i m a t i o n V i e w H o l d e r   O c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / G r i d S p a c i n g I t e m D e c o r a t i o n   H c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / A n i m a t i o n A d a p t e r K t   E c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / C a t e g o r y A d a p t e r   X c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / C a t e g o r y A d a p t e r $ C a t e g o r y V i e w H o l d e r   D c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / A n i m a t i o n C a t e g o r y   @ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / A n i m a t i o n I t e m   @ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l I t e m   @ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d S t a t u s   I c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l P r e l o a d S t a t u s   I c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d e d A n i m a t i o n I t e m   I c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d e d T h u m b n a i l I t e m   @ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d R e s u l t   H c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d R e s u l t $ S u c c e s s   H c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d R e s u l t $ F a i l u r e   N c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / P r e l o a d R e s u l t $ A l r e a d y E x i s t s   I c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l P r e l o a d R e s u l t   Q c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l P r e l o a d R e s u l t $ S u c c e s s   Q c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l P r e l o a d R e s u l t $ F a i l u r e   W c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / T h u m b n a i l P r e l o a d R e s u l t $ A l r e a d y E x i s t s   9 c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / O t h e r s F r a g m e n t   C c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / O t h e r s F r a g m e n t $ C o m p a n i o n   @ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r   J c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r $ C o m p a n i o n   Q c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r $ O t h e r s V i e w H o l d e r   [ c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r $ O t h e r s V i e w H o l d e r $ C o m p a n i o n   S c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r $ O t h e r s D i f f C a l l b a c k   > c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / d a t a / O t h e r s I t e m D a t a   H c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / d a t a / O t h e r s I t e m D a t a $ C o m p a n i o n   6 c o m / t q h i t / b a t t e r y / o n e / g l i d e / M e m o r y O p t i m i z e d G l i d e M o d u l e   @ c o m / t q h i t / b a t t e r y / o n e / g l i d e / M e m o r y O p t i m i z e d G l i d e M o d u l e $ C o m p a n i o n   7 c o m / t q h i t / b a t t e r y / o n e / g l i d e / M e m o r y O p t i m i z e d G l i d e O p t i o n s   . c o m / t q h i t / b a t t e r y / o n e / g l i d e / G l i d e M e m o r y M a n a g e r   < c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / a n i m a t i o n / A n i m a t i o n F i l e M a n a g e r   F c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / a n i m a t i o n / A n i m a t i o n F i l e M a n a g e r $ C o m p a n i o n   2 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g e S e s s i o n   < c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g e S e s s i o n $ C o m p a n i o n   ; c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g i n g S e s s i o n M a n a g e r   E c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g i n g S e s s i o n M a n a g e r $ C o m p a n i o n   8 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n   B c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n $ C o m p a n i o n   ? c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n M a n a g e r   I c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n M a n a g e r $ C o m p a n i o n   0 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / g r a p h / H i s t o r y E n t r y   2 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / g r a p h / H i s t o r y M a n a g e r   9 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / g r a p h / B a t t e r y H i s t o r y M a n a g e r   = c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / g r a p h / T e m p e r a t u r e H i s t o r y M a n a g e r   7 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r   A c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r $ C o m p a n i o n   C c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r $ Q u o t a S t a t u s   N c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r $ Q u o t a E n f o r c e m e n t R e s u l t   C c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r $ Q u o t a A c t i o n   H c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / q u o t a / S t o r a g e Q u o t a M a n a g e r $ Q u o t a U s a g e R e p o r t   = c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / s t o r a g e / O p t i m i z e d S t o r a g e M a n a g e r   G c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / s t o r a g e / O p t i m i z e d S t o r a g e M a n a g e r $ C o m p a n i o n   M c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / s t o r a g e / O p t i m i z e d S t o r a g e M a n a g e r $ S t o r a g e A n a l y s i s   K c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / s t o r a g e / O p t i m i z e d S t o r a g e M a n a g e r $ C l e a n u p R e s u l t   0 c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / t h e m e / T h e m e M a n a g e r   < c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / t h u m b n a i l / T h u m b n a i l F i l e M a n a g e r   F c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / t h u m b n a i l / T h u m b n a i l F i l e M a n a g e r $ C o m p a n i o n   > c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n P r e l o a d i n g R e p o s i t o r y   H c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n P r e l o a d i n g R e p o s i t o r y $ C o m p a n i o n   1 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t   F c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ N o A n i m a t i o n s P r o v i d e d   A c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ A l r e a d y U p T o D a t e   9 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ S u c c e s s   @ c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ P a r t i a l S u c c e s s   ; c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ A l l F a i l e d   7 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g R e s u l t $ E r r o r   0 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / P r e l o a d i n g S t a t s   4 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n R e p o s i t o r y   > c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n R e p o s i t o r y $ C o m p a n i o n   H c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n R e p o s i t o r y $ A n i m a t i o n A p p l y E n t r y   . c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A p p R e p o s i t o r y   8 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A p p R e p o s i t o r y $ C o m p a n i o n   2 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y   < c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y $ C o m p a n i o n   > c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e p o s i t o r y   H c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e p o s i t o r y $ C o m p a n i o n   : c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t u s   ? c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t u s $ I d l e   E c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t u s $ I n P r o g r e s s   D c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t u s $ C o m p l e t e d   @ c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t u s $ E r r o r   : c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t   B c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t $ S u c c e s s   I c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t $ P a r t i a l S u c c e s s   D c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t $ A l l F a i l e d   O c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t $ N o T h u m b n a i l s P r o v i d e d   @ c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e s u l t $ E r r o r   9 c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g S t a t s   4 c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e   > c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e $ C o m p a n i o n   : c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e H e l p e r   D c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e H e l p e r $ C o m p a n i o n   . c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / V i b r a t i o n S e r v i c e   8 c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / V i b r a t i o n S e r v i c e $ C o m p a n i o n   < c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n D a t a S e r v i c e   F c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n D a t a S e r v i c e $ C o m p a n i o n   : c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n P r e l o a d e r   D c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n P r e l o a d e r $ C o m p a n i o n   8 c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / P r e l o a d i n g S t a t u s   D c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r   N c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ C o m p a n i o n   X c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e   g c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e $ N o t I n i t i a l i z e d   e c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e $ I n i t i a l i z i n g   ^ c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e $ R e a d y   _ c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e $ F a i l e d   a c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g S t a t e $ T i m e d O u t   [ c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / F i r e b a s e I n i t i a l i z a t i o n M o n i t o r $ F i r e b a s e C o n f i g C a l l b a c k   A c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / L o c a l C o n f i g F a l l b a c k S e r v i c e   K c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / f i r e b a s e / L o c a l C o n f i g F a l l b a c k S e r v i c e $ C o m p a n i o n   D c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / o p t i m i z a t i o n / M e m o r y O p t i m i z a t i o n S e r v i c e   N c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / o p t i m i z a t i o n / M e m o r y O p t i m i z a t i o n S e r v i c e $ C o m p a n i o n   J c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / D e f e r r e d T h u m b n a i l P r e l o a d i n g S e r v i c e   T c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / D e f e r r e d T h u m b n a i l P r e l o a d i n g S e r v i c e $ C o m p a n i o n   E c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / M e m o r y A w a r e T h u m b n a i l P r e l o a d e r   O c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / M e m o r y A w a r e T h u m b n a i l P r e l o a d e r $ C o m p a n i o n   S c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / M e m o r y A w a r e T h u m b n a i l P r e l o a d e r $ L o a d i n g C o n f i g   < c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l D a t a S e r v i c e   F c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l D a t a S e r v i c e $ C o m p a n i o n   : c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l P r e l o a d e r   D c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l P r e l o a d e r $ C o m p a n i o n   * c o m / t q h i t / b a t t e r y / o n e / u t i l s / A n t i T h i e f U t i l s   7 c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a c k g r o u n d P e r m i s s i o n M a n a g e r   6 c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y C a l c u l a t o r D i s c h a r g e   ( c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y U t i l s   1 c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r   ; c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ C o m p a n i o n   B c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ P r e l o a d O p e r a t i o n   S c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ P r e l o a d O p e r a t i o n $ A n i m a t i o n P r e l o a d   S c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ P r e l o a d O p e r a t i o n $ T h u m b n a i l P r e l o a d   : c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ P r i o r i t y   ? c o m / t q h i t / b a t t e r y / o n e / u t i l s / C p u O p t i m i z e d P r e l o a d e r $ C p u U s a g e L e v e l   4 c o m / t q h i t / b a t t e r y / o n e / u t i l s / L o w P r i o r i t y T h r e a d F a c t o r y   7 c o m / t q h i t / b a t t e r y / o n e / u t i l s / N o r m a l P r i o r i t y T h r e a d F a c t o r y   5 c o m / t q h i t / b a t t e r y / o n e / u t i l s / H i g h P r i o r i t y T h r e a d F a c t o r y   ) c o m / t q h i t / b a t t e r y / o n e / u t i l s / D a t e T i m e U t i l s   ' c o m / t q h i t / b a t t e r y / o n e / u t i l s / D e v i c e U t i l s   2 c o m / t q h i t / b a t t e r y / o n e / u t i l s / F o r e g r o u n d S e r v i c e U t i l s   * c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r   4 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ C o m p a n i o n   ? c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ M e m o r y A n a l y s i s R e s u l t   > c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ M e m o r y P r e s s u r e L e v e l   ; c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ P e r f o r m a n c e I s s u e   4 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ I s s u e T y p e   3 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ S e v e r i t y   5 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ M e m o r y I n f o   6 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ S t o r a g e I n f o   4 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ C a c h e I n f o   6 c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ P r e l o a d I n f o   @ c o m / t q h i t / b a t t e r y / o n e / u t i l s / M e m o r y A n a l y z e r $ O p e r a t i o n M e m o r y R e s u l t   - c o m / t q h i t / b a t t e r y / o n e / u t i l s / N o t i f i c a t i o n U t i l s   2 c o m / t q h i t / b a t t e r y / o n e / u t i l s / O v e r l a y P e r m i s s i o n U t i l s   / c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r   9 c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r $ C o m p a n i o n   D c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r $ P e r f o r m a n c e V i o l a t i o n   A c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r $ V i o l a t i o n S e v e r i t y   > c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r $ M e m o r y S n a p s h o t   A c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r f o r m a n c e P r o f i l e r $ P e r f o r m a n c e R e p o r t   + c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r m i s s i o n U t i l s   - c o m / t q h i t / b a t t e r y / o n e / u t i l s / P r e l o a d i n g M o n i t o r   7 c o m / t q h i t / b a t t e r y / o n e / u t i l s / P r e l o a d i n g M o n i t o r $ C o m p a n i o n   & c o m / t q h i t / b a t t e r y / o n e / u t i l s / V i d e o U t i l s   0 c o m / t q h i t / b a t t e r y / o n e / u t i l s / V i d e o U t i l s $ C o m p a n i o n   , c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / A p p V i e w M o d e l   < c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / a n i m a t i o n / A n i m a t i o n V i e w M o d e l   8 c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / b a t t e r y / B a t t e r y V i e w M o d e l    . k o t l i n _ m o d u l e                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              