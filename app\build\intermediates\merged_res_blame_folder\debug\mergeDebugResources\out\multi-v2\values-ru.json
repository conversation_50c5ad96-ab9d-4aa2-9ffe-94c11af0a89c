{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-132:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4912bc3b25b094e82d07c57850b5737f\\transformed\\core-1.16.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "156,157,158,159,160,161,162,591", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "12569,12667,12769,12870,12971,13076,13179,51836", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "12662,12764,12865,12966,13071,13174,13291,51932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2a5d12d8d5558ab0105ad2ffa8804aca\\transformed\\navigation-ui-2.8.9\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,118", "endOffsets": "158,277"}, "to": {"startLines": "494,495", "startColumns": "4,4", "startOffsets": "43727,43835", "endColumns": "107,118", "endOffsets": "43830,43949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\50402698ac7a23eac4a4d32b68f3d8ff\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,999,1086,1234,1312,1388,1472,1542", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,77,75,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,994,1081,1153,1307,1383,1467,1537,1660"}, "to": {"startLines": "182,183,223,224,251,345,347,496,504,548,549,572,589,590,596,598,601", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14769,14862,19944,20042,23191,30296,30442,43954,44546,48419,48503,50127,51682,51760,52293,52420,52594", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,77,75,83,69,122", "endOffsets": "14857,14940,20037,20139,23278,30373,30527,44037,44623,48498,48585,50194,51755,51831,52372,52485,52712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\872bae3c38ca42cf14648f81ccb65b49\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "285,286,287,288,289,290,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "25897,25969,26030,26095,26161,26239,26313,26401,26487", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "25964,26025,26090,26156,26234,26308,26396,26482,26559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c045bfa763bb62c5417c6b4a71ceb2b6\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "607,608", "startColumns": "4,4", "startOffsets": "53190,53278", "endColumns": "87,90", "endOffsets": "53273,53364"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ru\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "123,193,247,300,353,404,459,511,561,620,692,745,795,847,898,20926,951,1030,1087,1137,1185,1237,1315,1384,1464,1542,1613,1673,23396,1728,1767,1934,1991,2093,2136,2204,21117,21940,22355,22486,2289,57,23349,21316,23222,2333,2382,2421,2509,2569,2644,3053,3465,11672,11739,11020,10937,11804,3512,3585,3662,3865,4146,4221,4279,4321,4388,4465,4515,21003,4584,4652,21242,4695,4774,4816,4890,5046,5117,5207,5288,5350,5437,21818,5499,5555,5611,5676,5747,5796,5867,5960,6014,6334,6388,6448,6919,7401,7469,7520,7664,7726,7997,8050,8477,8540,8671,8825,8970,9034,9082,9128,9202,9295,9382,9433,9556,9654,9712,9777,9844,10119,10385,10565,10728,10813,11891,11961,23024,12016,12058,12150,12215,12273,12495,12544,12617,12687,12744,12882,12941,13071,13291,13331,13396,13434,13472,13508,13544,13613,13659,13718,13770,13834,23083,13904,13971,14031,14074,14117,14200,14566,14620,14694,14866,14937,15008,15045,15080,15116,15154,15208,15271,15343,15398,15479,15626,21165,15662,15720,15774,15861,15933,16008,16117,16237,16308,16348,16427,21620,21449,21361,22260,22798,16737,16777,16827,16878,16941,17028,22403,17077,17163,17213,21072,17257,23148,17388,17478,17570,17629,17700,17878,17954,18009,21733,18069,18124,22843,18183,18263,18360,18440,18488,22188,18558,18638,18680,18736,18786,18904,19227,19302,19349,19407,19481,19539,19592,23288,19632,19700,19916,20009,20075,20144,20199,20272,20346,20380,21883,20438,20495,20543,20581,20635,20692,20754,20809,20845,20881", "endColumns": "68,52,51,51,49,53,50,48,57,70,51,48,50,49,51,75,77,55,48,46,50,76,67,78,76,69,58,53,64,37,165,55,100,41,66,83,46,246,46,310,42,64,45,43,64,47,37,86,58,73,407,410,45,65,63,650,81,83,71,75,201,279,73,56,40,65,75,48,67,67,66,41,72,77,40,72,154,69,88,79,60,85,60,63,54,54,63,69,47,69,91,52,318,52,58,469,480,66,49,142,60,269,51,425,61,129,152,143,62,46,44,72,91,85,49,121,96,56,63,65,273,264,178,161,83,77,68,53,57,40,90,63,56,220,47,71,68,55,136,57,128,218,38,63,36,36,34,34,67,44,57,50,62,68,63,65,58,41,41,81,364,52,72,170,69,69,35,33,34,36,52,61,70,53,79,145,34,75,56,52,85,70,73,107,118,69,38,77,308,111,169,86,93,43,38,48,49,61,85,47,81,84,48,42,43,129,72,88,90,57,69,176,74,53,58,83,53,57,179,78,95,78,46,68,70,78,40,54,48,116,321,73,45,56,72,56,51,38,59,66,214,91,64,67,53,71,72,32,56,55,55,46,36,52,55,60,53,34,34,43", "endOffsets": "187,241,294,347,398,453,505,555,614,686,739,789,841,892,945,20997,1024,1081,1131,1179,1231,1309,1378,1458,1536,1607,1667,1722,23456,1761,1928,1985,2087,2130,2198,2283,21159,22182,22397,22792,2327,117,23390,21355,23282,2376,2415,2503,2563,2638,3047,3459,3506,11733,11798,11666,11014,11883,3579,3656,3859,4140,4215,4273,4315,4382,4459,4509,4578,21066,4646,4689,21310,4768,4810,4884,5040,5111,5201,5282,5344,5431,5493,21877,5549,5605,5670,5741,5790,5861,5954,6008,6328,6382,6442,6913,7395,7463,7514,7658,7720,7991,8044,8471,8534,8665,8819,8964,9028,9076,9122,9196,9289,9376,9427,9550,9648,9706,9771,9838,10113,10379,10559,10722,10807,10886,11955,12010,23077,12052,12144,12209,12267,12489,12538,12611,12681,12738,12876,12935,13065,13285,13325,13390,13428,13466,13502,13538,13607,13653,13712,13764,13828,13898,23142,13965,14025,14068,14111,14194,14560,14614,14688,14860,14931,15002,15039,15074,15110,15148,15202,15265,15337,15392,15473,15620,15656,21236,15714,15768,15855,15927,16002,16111,16231,16302,16342,16421,16731,21727,21614,21443,22349,22837,16771,16821,16872,16935,17022,17071,22480,17157,17207,17251,21111,17382,23216,17472,17564,17623,17694,17872,17948,18003,18063,21812,18118,18177,23018,18257,18354,18434,18482,18552,22254,18632,18674,18730,18780,18898,19221,19296,19343,19401,19475,19533,19586,19626,23343,19694,19910,20003,20069,20138,20193,20266,20340,20374,20432,21934,20489,20537,20575,20629,20686,20748,20803,20839,20875,20920"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,75,79,80,81,82,83,84,85,86,87,88,89,91,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,143,144,145,151,152,153,154,155,163,164,165,168,169,170,171,172,173,174,175,176,177,178,179,180,203,204,205,206,209,210,211,212,213,214,215,216,217,218,219,222,225,226,227,228,229,230,231,233,234,235,236,237,238,239,242,243,244,246,247,248,249,250,252,254,255,256,257,259,314,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,339,340,341,343,344,346,348,349,355,356,357,360,361,362,363,364,365,366,424,425,426,427,441,442,490,497,498,499,501,502,506,507,508,509,510,511,512,513,523,524,525,526,527,528,529,531,532,533,534,535,536,537,538,539,540,542,543,544,545,546,550,551,552,553,554,555,563,564,571,573,574,575,576,577,578,579,580,582,583,584,585,586,587,588,592,593,595,600,602,603,604,605,606,655,656,657,659,660,661,664,665,666,667,669,670,671,672,673,674,675,676", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1394,1447,1499,1551,1601,1655,1706,1755,1813,1884,1936,1985,2036,2086,2138,2431,2509,2791,2840,3825,3876,4060,4128,4207,4564,4634,4693,4800,5072,5110,5276,5332,5433,5475,5542,5626,5673,5920,5967,6328,6371,6622,6668,6712,6777,6825,6863,6950,7009,7083,7491,7902,7948,8014,8078,8729,8811,8895,8967,9043,9245,9525,11648,11705,11746,12241,12317,12366,12434,12502,13296,13338,13411,13723,13764,13837,13992,14062,14151,14231,14292,14378,14439,14503,14558,14613,17288,17358,17406,17476,17750,17803,18122,18175,18234,18704,19185,19252,19302,19445,19506,19892,20144,20570,20632,20762,20915,21059,21122,21215,21260,21333,21425,21511,21561,21683,21953,22010,22074,22227,22501,22766,22945,23107,23283,23405,23474,23528,23586,23702,27890,28420,28477,28698,28746,28818,28887,28943,29080,29138,29267,29486,29525,29589,29626,29663,29698,29733,29933,29978,30036,30164,30227,30378,30532,30598,31005,31047,31089,31309,31674,31727,31800,31971,32041,32111,38405,38439,38474,38511,39541,39603,43506,44042,44122,44268,44354,44430,44705,44758,44844,44915,44989,45097,45216,45286,46229,46307,46616,46728,46898,46985,47079,47203,47242,47291,47341,47403,47489,47537,47619,47704,47753,47941,47985,48115,48188,48277,48590,48648,48718,48895,48970,49024,49490,49574,50069,50199,50379,50458,50554,50633,50680,50749,50820,50978,51019,51074,51123,51240,51562,51636,51937,51994,52236,52542,52717,52756,52816,52883,53098,56468,56533,56601,56728,56800,56873,57066,57123,57179,57235,57342,57379,57432,57488,57549,57603,57638,57673", "endColumns": "68,52,51,51,49,53,50,48,57,70,51,48,50,49,51,75,77,55,48,46,50,76,67,78,76,69,58,53,64,37,165,55,100,41,66,83,46,246,46,310,42,64,45,43,64,47,37,86,58,73,407,410,45,65,63,650,81,83,71,75,201,279,73,56,40,65,75,48,67,67,66,41,72,77,40,72,154,69,88,79,60,85,60,63,54,54,63,69,47,69,91,52,318,52,58,469,480,66,49,142,60,269,51,425,61,129,152,143,62,46,44,72,91,85,49,121,96,56,63,65,273,264,178,161,83,77,68,53,57,40,90,63,56,220,47,71,68,55,136,57,128,218,38,63,36,36,34,34,67,44,57,50,62,68,63,65,58,41,41,81,364,52,72,170,69,69,35,33,34,36,52,61,70,53,79,145,34,75,56,52,85,70,73,107,118,69,38,77,308,111,169,86,93,43,38,48,49,61,85,47,81,84,48,42,43,129,72,88,90,57,69,176,74,53,58,83,53,57,179,78,95,78,46,68,70,78,40,54,48,116,321,73,45,56,72,56,51,38,59,66,214,91,64,67,53,71,72,32,56,55,55,46,36,52,55,60,53,34,34,43", "endOffsets": "1389,1442,1494,1546,1596,1650,1701,1750,1808,1879,1931,1980,2031,2081,2133,2209,2504,2560,2835,2882,3871,3948,4123,4202,4279,4629,4688,4742,4860,5105,5271,5327,5428,5470,5537,5621,5668,5915,5962,6273,6366,6431,6663,6707,6772,6820,6858,6945,7004,7078,7486,7897,7943,8009,8073,8724,8806,8890,8962,9038,9240,9520,9594,11700,11741,11807,12312,12361,12429,12497,12564,13333,13406,13484,13759,13832,13987,14057,14146,14226,14287,14373,14434,14498,14553,14608,14672,17353,17401,17471,17563,17798,18117,18170,18229,18699,19180,19247,19297,19440,19501,19771,19939,20565,20627,20757,20910,21054,21117,21164,21255,21328,21420,21506,21556,21678,21775,22005,22069,22135,22496,22761,22940,23102,23186,23356,23469,23523,23581,23622,23788,27949,28472,28693,28741,28813,28882,28938,29075,29133,29262,29481,29520,29584,29621,29658,29693,29728,29796,29973,30031,30082,30222,30291,30437,30593,30652,31042,31084,31166,31669,31722,31795,31966,32036,32106,32142,38434,38469,38506,38559,39598,39669,43555,44117,44263,44298,44425,44482,44753,44839,44910,44984,45092,45211,45281,45320,46302,46611,46723,46893,46980,47074,47118,47237,47286,47336,47398,47484,47532,47614,47699,47748,47791,47980,48110,48183,48272,48363,48643,48713,48890,48965,49019,49078,49569,49623,50122,50374,50453,50549,50628,50675,50744,50815,50894,51014,51069,51118,51235,51557,51631,51677,51989,52062,52288,52589,52751,52811,52878,53093,53185,56528,56596,56650,56795,56868,56901,57118,57174,57230,57277,57374,57427,57483,57544,57598,57633,57668,57712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\72a95338dd352f6906f9af3c130aa8a2\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,507,612,888,980,1074,1169,1262,1357,1451,1547,1642,1734,2021,2442,2556,2822", "endColumns": "114,101,104,120,91,93,94,92,94,93,95,94,91,91,106,113,165,81", "endOffsets": "215,317,607,728,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,2123,2551,2717,2899"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,568", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2214,2329,2565,2670,2887,2979,3073,3168,3261,3356,3450,3546,3641,3733,3953,4284,4398,49817", "endColumns": "114,101,104,120,91,93,94,92,94,93,95,94,91,91,106,113,165,81", "endOffsets": "2324,2426,2665,2786,2974,3068,3163,3256,3351,3445,3541,3636,3728,3820,4055,4393,4559,49894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\65a6a6823d98194731daa68a1b3ace70\\transformed\\jetified-mobileads-7.14.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,28,29,30,31,32,33,34,35,36,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,108,163,247,315,365,424,482,551,621,683,737,783,870,914,959,1029,1091,1143,1208,1276,1376,1446,1514,1584,1701,1752,1811,1845,1896,1955,2028,2085,2128,2180", "endColumns": "52,54,83,67,49,58,57,68,69,61,53,45,86,43,44,69,61,51,64,67,99,69,67,69,81,50,58,33,50,58,72,56,42,51,72", "endOffsets": "103,158,242,310,360,419,477,546,616,678,732,778,865,909,954,1024,1086,1138,1203,1271,1371,1441,1509,1579,1661,1747,1806,1840,1891,1950,2023,2080,2123,2175,2248"}, "to": {"startLines": "74,76,77,78,90,93,94,95,202,220,221,232,245,253,319,337,338,350,351,352,353,358,359,443,444,500,503,522,547,565,566,567,597,599,658", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4747,4865,4920,5004,6278,6436,6495,6553,17218,19776,19838,21169,22140,23361,28375,29801,29871,30657,30709,30774,30842,31171,31241,39674,39744,44303,44487,46195,48368,49628,49687,49760,52377,52490,56655", "endColumns": "52,54,83,67,49,58,57,68,69,61,53,45,86,43,44,69,61,51,64,67,99,69,67,69,81,50,58,33,50,58,72,56,42,51,72", "endOffsets": "4795,4915,4999,5067,6323,6490,6548,6617,17283,19833,19887,21210,22222,23400,28415,29866,29928,30704,30769,30837,30937,31236,31304,39739,39821,44349,44541,46224,48414,49682,49755,49812,52415,52537,56723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9b1d1c56b528abe32d69bef2c9ce868e\\transformed\\jetified-bigo-ads-5.3.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,265,326,394,476,566,645,708,771,826,1187,1274,1360,1418,1483,1545,1600,1665,1717,1792,1854,1909,1961,2026", "endColumns": "72,76,59,60,67,81,89,78,62,62,54,360,86,85,57,64,61,54,64,51,74,61,54,51,64,77", "endOffsets": "123,200,260,321,389,471,561,640,703,766,821,1182,1269,1355,1413,1478,1540,1595,1660,1712,1787,1849,1904,1956,2021,2099"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9599,9672,9749,9809,9870,9938,10020,10110,10189,10252,10315,10370,10731,10818,10904,10962,11027,11089,11144,11209,11261,11336,11398,11453,11505,11570", "endColumns": "72,76,59,60,67,81,89,78,62,62,54,360,86,85,57,64,61,54,64,51,74,61,54,51,64,77", "endOffsets": "9667,9744,9804,9865,9933,10015,10105,10184,10247,10310,10365,10726,10813,10899,10957,11022,11084,11139,11204,11256,11331,11393,11448,11500,11565,11643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7be73d2863fd2f56cbc8ece5355c1b16\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3727,3788,3873,3958,4021", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3722,3783,3868,3953,4016,4085"}, "to": {"startLines": "2,11,17,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,294,295,296,297,298,299,300,301,302,303,304,307,308,309,310,311,312", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,375,714,23859,23942,24025,24108,24198,24298,24369,24442,24541,24642,24715,24787,24852,24930,25042,25153,25270,25347,25442,25514,25587,25675,25763,25832,26564,26617,26679,26727,26788,26855,26923,26989,27071,27129,27186,27383,27435,27496,27581,27666,27729", "endLines": "10,16,22,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,294,295,296,297,298,299,300,301,302,303,304,307,308,309,310,311,312", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "370,709,1039,23937,24020,24103,24193,24293,24364,24437,24536,24637,24710,24782,24847,24925,25037,25148,25265,25342,25437,25509,25582,25670,25758,25827,25892,26612,26674,26722,26783,26850,26918,26984,27066,27124,27181,27247,27430,27491,27576,27661,27724,27793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c9717de49df575d5db38658124185973\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "23,146,147,148,149,150,166,167,181,258,260,315,342,354,428,429,430,431,432,433,434,435,436,437,438,439,440,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,530,569,570,581", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1044,11812,11890,11968,12052,12150,13489,13586,14677,23627,23793,27954,30087,30942,38564,38682,38743,38808,38865,38935,38996,39050,39166,39223,39285,39339,39413,39826,39914,40001,40104,40196,40282,40419,40503,40588,40722,40813,40889,40943,40994,41060,41132,41210,41281,41363,41443,41519,41596,41673,41780,41869,41942,42032,42127,42201,42282,42375,42430,42511,42577,42663,42748,42810,42874,42937,43009,43107,43206,43301,43393,43451,47123,49899,49993,50899", "endLines": "28,146,147,148,149,150,166,167,181,258,260,315,342,354,428,429,430,431,432,433,434,435,436,437,438,439,440,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,530,569,570,581", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "1320,11885,11963,12047,12145,12236,13581,13718,14764,23697,23854,28048,30159,31000,38677,38738,38803,38860,38930,38991,39045,39161,39218,39280,39334,39408,39536,39909,39996,40099,40191,40277,40414,40498,40583,40717,40808,40884,40938,40989,41055,41127,41205,41276,41358,41438,41514,41591,41668,41775,41864,41937,42027,42122,42196,42277,42370,42425,42506,42572,42658,42743,42805,42869,42932,43004,43102,43201,43296,43388,43446,43501,47198,49988,50064,50973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1575b72cf39be0513007f700cf9dbd85\\transformed\\jetified-pag-sdk-ad-unfat-7206-20250616114438\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,179,243,351,392,442,498,556,616,679,743,786,882,998,1067,1129,1172,1241,1288,1374,1448,1506,1556,1639,1686,1745,1802,1876,1922,1995,2113,2166,2239,2330,2394,2456,2534,2593,2641,2694,2752,2812,2877,2963,3084", "endColumns": "60,62,63,107,40,49,55,57,59,62,63,42,95,115,68,61,42,68,46,85,73,57,49,82,46,58,56,73,45,72,117,52,72,90,63,61,77,58,47,52,57,59,64,85,120,69", "endOffsets": "111,174,238,346,387,437,493,551,611,674,738,781,877,993,1062,1124,1167,1236,1283,1369,1443,1501,1551,1634,1681,1740,1797,1871,1917,1990,2108,2161,2234,2325,2389,2451,2529,2588,2636,2689,2747,2807,2872,2958,3079,3149"}, "to": {"startLines": "609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "53369,53430,53493,53557,53665,53706,53756,53812,53870,53930,53993,54057,54100,54196,54312,54381,54443,54486,54555,54602,54688,54762,54820,54870,54953,55000,55059,55116,55190,55236,55309,55427,55480,55553,55644,55708,55770,55848,55907,55955,56008,56066,56126,56191,56277,56398", "endColumns": "60,62,63,107,40,49,55,57,59,62,63,42,95,115,68,61,42,68,46,85,73,57,49,82,46,58,56,73,45,72,117,52,72,90,63,61,77,58,47,52,57,59,64,85,120,69", "endOffsets": "53425,53488,53552,53660,53701,53751,53807,53865,53925,53988,54052,54095,54191,54307,54376,54438,54481,54550,54597,54683,54757,54815,54865,54948,54995,55054,55111,55185,55231,55304,55422,55475,55548,55639,55703,55765,55843,55902,55950,56003,56061,56121,56186,56272,56393,56463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f4941c30c1ad49227b5c20962bf70d62\\transformed\\jetified-play-services-ads-24.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,588,652,814,952,1084,1134,1194,1338,1426,1475,1556,1593,1630,1677,1758,1805", "endColumns": "41,49,62,63,161,137,131,49,59,143,87,48,80,36,36,46,80,46,55", "endOffsets": "240,290,353,651,813,951,1083,1133,1193,1337,1425,1474,1555,1592,1629,1676,1757,1804,1860"}, "to": {"startLines": "491,492,493,514,515,516,517,518,519,520,521,556,557,558,559,560,561,562,668", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "43560,43606,43660,45325,45393,45559,45701,45837,45891,45955,46103,49083,49136,49221,49262,49303,49354,49439,57282", "endColumns": "45,53,66,67,165,141,135,53,63,147,91,52,84,40,40,50,84,50,59", "endOffsets": "43601,43655,43722,45388,45554,45696,45832,45886,45950,46098,46190,49131,49216,49257,49298,49349,49434,49485,57337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b8b1eaa684e277376481359ae9741c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "184,185,186,187,188,189,190,191,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14945,15052,15218,15344,15454,15596,15725,15840,16101,16282,16389,16552,16678,16845,17003,17072,17132", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "15047,15213,15339,15449,15591,15720,15835,15939,16277,16384,16547,16673,16840,16998,17067,17127,17213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f2bddcf99854293f296b0d3ec5a62fcd\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "208,316,317,318", "startColumns": "4,4,4,4", "startOffsets": "17642,28053,28158,28270", "endColumns": "107,104,111,104", "endOffsets": "17745,28153,28265,28370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2d4ed03ab3978d697e580782f6ec8862\\transformed\\jetified-exoplayer-ui-2.18.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "58,59", "startColumns": "4,4", "startOffsets": "3675,3740", "endColumns": "64,65", "endOffsets": "3735,3801"}, "to": {"startLines": "305,306", "startColumns": "4,4", "startOffsets": "27252,27317", "endColumns": "64,65", "endOffsets": "27312,27378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1057906e728c9c7b13b7392bddc5c3a4\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "192", "startColumns": "4", "startOffsets": "15944", "endColumns": "156", "endOffsets": "16096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2f209d101051ecb1c65632381d22aacb\\transformed\\preference-1.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "207,313,505,541,594,662,663", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17568,27798,44628,47796,52067,56906,56988", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "17637,27885,44700,47936,52231,56983,57061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1b81c398e9455b04c7c579a154b9259d\\transformed\\jetified-material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4650,4744,4831,4939,5019,5103,5201,5302,5396,5491,5579,5686,5784,5883,6030,6110,6216", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4645,4739,4826,4934,5014,5098,5196,5297,5391,5486,5574,5681,5779,5878,6025,6105,6211,6308"}, "to": {"startLines": "367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32147,32265,32381,32499,32617,32716,32813,32927,33068,33185,33325,33409,33507,33600,33698,33813,33936,34039,34168,34296,34422,34602,34726,34849,34976,35096,35190,35290,35411,35544,35642,35756,35863,35995,36133,36243,36343,36428,36523,36619,36742,36836,36923,37031,37111,37195,37293,37394,37488,37583,37671,37778,37876,37975,38122,38202,38308", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "32260,32376,32494,32612,32711,32808,32922,33063,33180,33320,33404,33502,33595,33693,33808,33931,34034,34163,34291,34417,34597,34721,34844,34971,35091,35185,35285,35406,35539,35637,35751,35858,35990,36128,36238,36338,36423,36518,36614,36737,36831,36918,37026,37106,37190,37288,37389,37483,37578,37666,37773,37871,37970,38117,38197,38303,38400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2cfb4b9f48d45f96c328421e469b00b\\transformed\\jetified-div-31.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "240,241", "startColumns": "4,4", "startOffsets": "21780,21865", "endColumns": "84,87", "endOffsets": "21860,21948"}}]}]}