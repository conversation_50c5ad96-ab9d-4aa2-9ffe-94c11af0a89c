@echo off
REM Firebase Remote Config Fallback Testing Script for TJ_BatteryOne
REM Tests various Firebase initialization scenarios and fallback mechanisms
REM Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo ========================================
echo Firebase Remote Config Fallback Testing
echo ========================================
echo.

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set DEVICE=emulator-5554

echo Using ADB: %ADB_PATH%
echo Target Device: %DEVICE%
echo Application ID: %APP_ID%
echo.

REM Check if device is connected
echo [1/7] Checking device connection...
%ADB_PATH% -s %DEVICE% shell echo "Device connected" > nul 2>&1
if errorlevel 1 (
    echo ERROR: Device %DEVICE% not found or not connected
    echo Please ensure the emulator is running and connected
    pause
    exit /b 1
)
echo Device %DEVICE% is connected
echo.

REM Test 1: Cold Start Performance with Firebase Monitoring
echo [2/7] Testing cold start performance with Firebase monitoring...
echo Forcing app stop and clearing cache...
%ADB_PATH% -s %DEVICE% shell am force-stop %APP_ID%
%ADB_PATH% -s %DEVICE% shell pm clear %APP_ID%

echo Starting app with timing measurement...
%ADB_PATH% -s %DEVICE% shell am start -W -n %APP_ID%/.activity.main.MainActivity

echo Monitoring Firebase initialization logs for 10 seconds...
timeout /t 2 > nul
%ADB_PATH% -s %DEVICE% logcat -s FIREBASE_MONITOR:* DEFERRED_THUMBNAIL_PRELOAD_ENHANCED:* THUMBNAIL_PRELOAD_ENHANCED:* THUMBNAIL_FALLBACK:* FALLBACK_CONFIG:* -d
echo.

REM Test 2: Network Disabled Scenario
echo [3/7] Testing Firebase fallback with network disabled...
echo Disabling network connectivity...
%ADB_PATH% -s %DEVICE% shell svc wifi disable
%ADB_PATH% -s %DEVICE% shell svc data disable

echo Forcing app restart...
%ADB_PATH% -s %DEVICE% shell am force-stop %APP_ID%
timeout /t 2 > nul
%ADB_PATH% -s %DEVICE% shell am start -n %APP_ID%/.activity.main.MainActivity

echo Monitoring fallback mechanism for 15 seconds...
timeout /t 3 > nul
%ADB_PATH% -s %DEVICE% logcat -s FIREBASE_MONITOR:* THUMBNAIL_FALLBACK:* FALLBACK_CONFIG:* -d

echo Re-enabling network...
%ADB_PATH% -s %DEVICE% shell svc wifi enable
%ADB_PATH% -s %DEVICE% shell svc data enable
echo.

REM Test 3: Firebase Timeout Scenario
echo [4/7] Testing Firebase timeout handling...
echo This test simulates slow Firebase initialization
echo Monitoring timeout and fallback behavior...

%ADB_PATH% -s %DEVICE% shell am force-stop %APP_ID%
timeout /t 2 > nul
%ADB_PATH% -s %DEVICE% shell am start -n %APP_ID%/.activity.main.MainActivity

echo Monitoring for Firebase timeout logs (30 seconds)...
timeout /t 5 > nul
%ADB_PATH% -s %DEVICE% logcat -s FIREBASE_MONITOR:* DEFERRED_THUMBNAIL_PRELOAD_ENHANCED:* -d | findstr /i "timeout\|fallback\|ready\|failed"
echo.

REM Test 4: Animation Fragment Loading Performance
echo [5/7] Testing animation fragment loading with fallback...
echo Navigating to animation fragment...

REM Simulate navigation to animation fragment (this would need to be adapted based on actual UI)
echo Monitoring animation data loading...
timeout /t 3 > nul
%ADB_PATH% -s %DEVICE% logcat -s AnimationDataService:* THUMBNAIL_PRELOAD_ENHANCED:* -d | findstr /i "categories\|fallback\|loading"
echo.

REM Test 5: Thumbnail Preloading Verification
echo [6/7] Verifying thumbnail preloading behavior...
echo Checking thumbnail extraction and preloading logs...

%ADB_PATH% -s %DEVICE% logcat -s ThumbnailDataService:* THUMBNAIL_EXTRACTION_SUMMARY:* -d | findstr /i "anime\|cartoon\|extracted\|preload"
echo.

REM Test 6: Configuration Values Verification
echo [7/7] Verifying configuration values fallback...
echo Checking if fallback configuration values are being used...

%ADB_PATH% -s %DEVICE% logcat -s FALLBACK_CONFIG:* -d | findstr /i "bn_enable\|aoa_enable\|animation_json"
echo.

echo ========================================
echo Testing Summary
echo ========================================
echo.
echo Tests completed. Key areas verified:
echo 1. Cold start performance with Firebase monitoring
echo 2. Network disabled fallback behavior
echo 3. Firebase timeout handling
echo 4. Animation fragment loading with fallback
echo 5. Thumbnail preloading verification
echo 6. Configuration values fallback
echo.
echo Check the logs above for:
echo - FIREBASE_MONITOR logs showing initialization status
echo - THUMBNAIL_FALLBACK logs showing fallback strategy
echo - FALLBACK_CONFIG logs showing local configuration usage
echo - Performance timing logs for startup optimization
echo.

REM Performance Benchmarks Check
echo Performance Benchmarks to Verify:
echo - Cold start: Should complete within 3 seconds
echo - Firebase monitoring: Should start within 100ms
echo - Fallback activation: Should occur within 5 seconds if Firebase fails
echo - Thumbnail preloading: Should start within 2 seconds (with or without Firebase)
echo.

echo Test completed. Press any key to exit...
pause > nul
