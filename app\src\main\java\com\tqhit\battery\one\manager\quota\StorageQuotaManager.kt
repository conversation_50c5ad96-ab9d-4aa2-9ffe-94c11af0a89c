package com.tqhit.battery.one.manager.quota

import android.content.Context
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Storage quota management system for TJ_BatteryOne app.
 * Enforces storage limits, implements intelligent cleanup strategies, and provides usage reporting.
 * 
 * Quota Limits:
 * - Video cache: 50MB maximum
 * - Thumbnail cache: 10MB maximum
 * - Total app cache: 60MB maximum
 * - Emergency cleanup threshold: 80% of quota
 * - Critical cleanup threshold: 95% of quota
 */
@Singleton
class StorageQuotaManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val storageManager: OptimizedStorageManager,
    private val preferencesHelper: PreferencesHelper
) {
    companion object {
        private const val TAG = "StorageQuotaManager"
        
        // Quota limits (in bytes)
        private const val VIDEO_QUOTA_BYTES = 50L * 1024 * 1024 // 50MB
        private const val THUMBNAIL_QUOTA_BYTES = 10L * 1024 * 1024 // 10MB
        private const val TOTAL_QUOTA_BYTES = VIDEO_QUOTA_BYTES + THUMBNAIL_QUOTA_BYTES // 60MB
        
        // Cleanup thresholds
        private const val WARNING_THRESHOLD = 0.7 // 70%
        private const val EMERGENCY_THRESHOLD = 0.8 // 80%
        private const val CRITICAL_THRESHOLD = 0.95 // 95%
        
        // Preferences keys
        private const val KEY_QUOTA_WARNINGS_ENABLED = "quota_warnings_enabled"
        private const val KEY_AUTO_CLEANUP_ENABLED = "auto_cleanup_enabled"
        private const val KEY_LAST_QUOTA_CHECK = "last_quota_check"
        private const val KEY_QUOTA_VIOLATIONS = "quota_violations"
        
        // Conversion constants
        private const val BYTES_TO_MB = 1024 * 1024L
    }
    
    // Quota status
    private val _quotaStatus = MutableStateFlow(QuotaStatus.NORMAL)
    val quotaStatus: StateFlow<QuotaStatus> = _quotaStatus
    
    // Quota enforcement settings
    private val _isAutoCleanupEnabled = MutableStateFlow(true)
    val isAutoCleanupEnabled: StateFlow<Boolean> = _isAutoCleanupEnabled
    
    private val _areWarningsEnabled = MutableStateFlow(true)
    val areWarningsEnabled: StateFlow<Boolean> = _areWarningsEnabled
    
    /**
     * Quota status levels.
     */
    enum class QuotaStatus {
        NORMAL,     // <70% of quota used
        WARNING,    // 70-80% of quota used
        EMERGENCY,  // 80-95% of quota used
        CRITICAL    // >95% of quota used
    }
    
    /**
     * Quota enforcement result.
     */
    data class QuotaEnforcementResult(
        val wasEnforced: Boolean,
        val actionTaken: QuotaAction,
        val spaceFreesMB: Long,
        val newQuotaStatus: QuotaStatus,
        val message: String
    )
    
    /**
     * Actions taken during quota enforcement.
     */
    enum class QuotaAction {
        NONE,
        WARNING_ISSUED,
        CLEANUP_PERFORMED,
        EMERGENCY_CLEANUP,
        CRITICAL_CLEANUP,
        PRELOADING_DISABLED
    }
    
    /**
     * Detailed quota usage report.
     */
    data class QuotaUsageReport(
        val videoUsageMB: Long,
        val thumbnailUsageMB: Long,
        val totalUsageMB: Long,
        val videoQuotaMB: Long,
        val thumbnailQuotaMB: Long,
        val totalQuotaMB: Long,
        val videoUsagePercent: Double,
        val thumbnailUsagePercent: Double,
        val totalUsagePercent: Double,
        val quotaStatus: QuotaStatus,
        val recommendedActions: List<String>,
        val timeUntilCleanup: Long? = null
    )
    
    init {
        // Load settings from preferences
        _isAutoCleanupEnabled.value = preferencesHelper.getBoolean(KEY_AUTO_CLEANUP_ENABLED, true)
        _areWarningsEnabled.value = preferencesHelper.getBoolean(KEY_QUOTA_WARNINGS_ENABLED, true)
    }
    
    /**
     * Checks current quota usage and enforces limits if necessary.
     */
    suspend fun enforceQuota(): QuotaEnforcementResult = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "QUOTA_ENFORCEMENT: Starting quota enforcement check")
        
        try {
            val storageAnalysis = storageManager.analyzeStorage()
            val quotaReport = generateQuotaReport(storageAnalysis)
            
            val previousStatus = _quotaStatus.value
            val newStatus = quotaReport.quotaStatus
            _quotaStatus.value = newStatus
            
            // Determine action based on quota status
            val action = when (newStatus) {
                QuotaStatus.NORMAL -> {
                    if (previousStatus != QuotaStatus.NORMAL) {
                        BatteryLogger.d(TAG, "QUOTA_ENFORCEMENT: Quota usage returned to normal")
                    }
                    QuotaAction.NONE
                }
                
                QuotaStatus.WARNING -> {
                    if (_areWarningsEnabled.value) {
                        BatteryLogger.w(TAG, "QUOTA_ENFORCEMENT: Quota warning - ${quotaReport.totalUsagePercent}% used")
                        QuotaAction.WARNING_ISSUED
                    } else {
                        QuotaAction.NONE
                    }
                }
                
                QuotaStatus.EMERGENCY -> {
                    if (_isAutoCleanupEnabled.value) {
                        BatteryLogger.w(TAG, "QUOTA_ENFORCEMENT: Emergency cleanup triggered - ${quotaReport.totalUsagePercent}% used")
                        performEmergencyCleanup()
                        QuotaAction.EMERGENCY_CLEANUP
                    } else {
                        QuotaAction.WARNING_ISSUED
                    }
                }
                
                QuotaStatus.CRITICAL -> {
                    BatteryLogger.e(TAG, "QUOTA_ENFORCEMENT: Critical quota exceeded - ${quotaReport.totalUsagePercent}% used")
                    performCriticalCleanup()
                    QuotaAction.CRITICAL_CLEANUP
                }
            }
            
            // Calculate space freed if cleanup was performed
            val spaceFreesMB = if (action == QuotaAction.EMERGENCY_CLEANUP || action == QuotaAction.CRITICAL_CLEANUP) {
                val newAnalysis = storageManager.analyzeStorage()
                (storageAnalysis.totalCacheSizeMB - newAnalysis.totalCacheSizeMB)
            } else {
                0L
            }
            
            // Record quota check
            preferencesHelper.saveLong(KEY_LAST_QUOTA_CHECK, System.currentTimeMillis())
            
            // Record violations if any
            if (newStatus == QuotaStatus.CRITICAL) {
                recordQuotaViolation()
            }
            
            val result = QuotaEnforcementResult(
                wasEnforced = action != QuotaAction.NONE,
                actionTaken = action,
                spaceFreesMB = spaceFreesMB,
                newQuotaStatus = newStatus,
                message = generateEnforcementMessage(action, quotaReport)
            )
            
            BatteryLogger.d(TAG, "QUOTA_ENFORCEMENT: Completed - Action: $action, Status: $newStatus")
            result
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "QUOTA_ENFORCEMENT: Error during quota enforcement", e)
            QuotaEnforcementResult(
                wasEnforced = false,
                actionTaken = QuotaAction.NONE,
                spaceFreesMB = 0L,
                newQuotaStatus = QuotaStatus.NORMAL,
                message = "Quota enforcement failed: ${e.message}"
            )
        }
    }
    
    /**
     * Generates detailed quota usage report.
     */
    suspend fun generateQuotaReport(): QuotaUsageReport = withContext(Dispatchers.IO) {
        val storageAnalysis = storageManager.analyzeStorage()
        generateQuotaReport(storageAnalysis)
    }
    
    /**
     * Generates quota report from storage analysis.
     */
    private fun generateQuotaReport(storageAnalysis: OptimizedStorageManager.StorageAnalysis): QuotaUsageReport {
        val videoUsageMB = storageAnalysis.videoCacheSizeMB
        val thumbnailUsageMB = storageAnalysis.thumbnailCacheSizeMB
        val totalUsageMB = videoUsageMB + thumbnailUsageMB
        
        val videoQuotaMB = VIDEO_QUOTA_BYTES / BYTES_TO_MB
        val thumbnailQuotaMB = THUMBNAIL_QUOTA_BYTES / BYTES_TO_MB
        val totalQuotaMB = TOTAL_QUOTA_BYTES / BYTES_TO_MB
        
        val videoUsagePercent = (videoUsageMB.toDouble() / videoQuotaMB.toDouble()) * 100
        val thumbnailUsagePercent = (thumbnailUsageMB.toDouble() / thumbnailQuotaMB.toDouble()) * 100
        val totalUsagePercent = (totalUsageMB.toDouble() / totalQuotaMB.toDouble()) * 100
        
        val quotaStatus = calculateQuotaStatus(totalUsagePercent)
        val recommendedActions = generateRecommendedActions(quotaStatus, videoUsagePercent, thumbnailUsagePercent)
        
        return QuotaUsageReport(
            videoUsageMB = videoUsageMB,
            thumbnailUsageMB = thumbnailUsageMB,
            totalUsageMB = totalUsageMB,
            videoQuotaMB = videoQuotaMB,
            thumbnailQuotaMB = thumbnailQuotaMB,
            totalQuotaMB = totalQuotaMB,
            videoUsagePercent = videoUsagePercent,
            thumbnailUsagePercent = thumbnailUsagePercent,
            totalUsagePercent = totalUsagePercent,
            quotaStatus = quotaStatus,
            recommendedActions = recommendedActions
        )
    }
    
    /**
     * Checks if a storage operation is allowed under current quota.
     */
    suspend fun isOperationAllowed(requiredSpaceMB: Long, operationType: String): Boolean = withContext(Dispatchers.IO) {
        val currentReport = generateQuotaReport()
        val projectedUsage = currentReport.totalUsageMB + requiredSpaceMB
        val projectedPercent = (projectedUsage.toDouble() / currentReport.totalQuotaMB.toDouble()) * 100
        
        val isAllowed = when (operationType) {
            "video_preload" -> {
                val projectedVideoUsage = currentReport.videoUsageMB + requiredSpaceMB
                projectedVideoUsage <= currentReport.videoQuotaMB && projectedPercent < CRITICAL_THRESHOLD * 100
            }
            "thumbnail_preload" -> {
                val projectedThumbnailUsage = currentReport.thumbnailUsageMB + requiredSpaceMB
                projectedThumbnailUsage <= currentReport.thumbnailQuotaMB && projectedPercent < CRITICAL_THRESHOLD * 100
            }
            else -> {
                projectedPercent < CRITICAL_THRESHOLD * 100
            }
        }
        
        BatteryLogger.d(TAG, "OPERATION_CHECK: $operationType requiring ${requiredSpaceMB}MB - Allowed: $isAllowed (projected: ${projectedPercent}%)")
        
        isAllowed
    }
    
    /**
     * Enables or disables automatic cleanup.
     */
    fun setAutoCleanupEnabled(enabled: Boolean) {
        _isAutoCleanupEnabled.value = enabled
        preferencesHelper.saveBoolean(KEY_AUTO_CLEANUP_ENABLED, enabled)
        BatteryLogger.d(TAG, "AUTO_CLEANUP: Auto cleanup ${if (enabled) "enabled" else "disabled"}")
    }
    
    /**
     * Enables or disables quota warnings.
     */
    fun setWarningsEnabled(enabled: Boolean) {
        _areWarningsEnabled.value = enabled
        preferencesHelper.saveBoolean(KEY_QUOTA_WARNINGS_ENABLED, enabled)
        BatteryLogger.d(TAG, "QUOTA_WARNINGS: Warnings ${if (enabled) "enabled" else "disabled"}")
    }
    
    /**
     * Gets quota violation history.
     */
    fun getQuotaViolationCount(): Int {
        return preferencesHelper.getInt(KEY_QUOTA_VIOLATIONS, 0)
    }
    
    /**
     * Calculates quota status based on usage percentage.
     */
    private fun calculateQuotaStatus(usagePercent: Double): QuotaStatus {
        return when {
            usagePercent >= CRITICAL_THRESHOLD * 100 -> QuotaStatus.CRITICAL
            usagePercent >= EMERGENCY_THRESHOLD * 100 -> QuotaStatus.EMERGENCY
            usagePercent >= WARNING_THRESHOLD * 100 -> QuotaStatus.WARNING
            else -> QuotaStatus.NORMAL
        }
    }
    
    /**
     * Generates recommended actions based on quota status.
     */
    private fun generateRecommendedActions(
        quotaStatus: QuotaStatus,
        videoUsagePercent: Double,
        thumbnailUsagePercent: Double
    ): List<String> {
        val actions = mutableListOf<String>()
        
        when (quotaStatus) {
            QuotaStatus.NORMAL -> {
                actions.add("Quota usage is within normal limits")
            }
            QuotaStatus.WARNING -> {
                actions.add("Consider cleaning up old cached files")
                if (videoUsagePercent > thumbnailUsagePercent) {
                    actions.add("Video cache is using more space - prioritize video cleanup")
                } else {
                    actions.add("Thumbnail cache is using more space - prioritize thumbnail cleanup")
                }
            }
            QuotaStatus.EMERGENCY -> {
                actions.add("Immediate cleanup recommended")
                actions.add("Reduce preloading batch sizes")
                actions.add("Enable automatic cleanup if disabled")
            }
            QuotaStatus.CRITICAL -> {
                actions.add("Critical: Immediate cleanup required")
                actions.add("Disable preloading until space is freed")
                actions.add("Consider reducing cache quotas")
            }
        }
        
        return actions
    }

    /**
     * Performs emergency cleanup when quota threshold is exceeded.
     */
    private suspend fun performEmergencyCleanup(): OptimizedStorageManager.CleanupResult {
        BatteryLogger.w(TAG, "EMERGENCY_CLEANUP: Starting emergency cleanup")
        return storageManager.performIntelligentCleanup()
    }

    /**
     * Performs critical cleanup when quota is severely exceeded.
     */
    private suspend fun performCriticalCleanup(): OptimizedStorageManager.CleanupResult {
        BatteryLogger.e(TAG, "CRITICAL_CLEANUP: Starting critical cleanup")

        // More aggressive cleanup for critical situations
        val result = storageManager.performIntelligentCleanup()

        // If still over quota after cleanup, disable preloading temporarily
        val newAnalysis = storageManager.analyzeStorage()
        val newUsagePercent = ((newAnalysis.totalCacheSizeMB.toDouble() / (TOTAL_QUOTA_BYTES / BYTES_TO_MB).toDouble()) * 100)

        if (newUsagePercent > EMERGENCY_THRESHOLD * 100) {
            BatteryLogger.e(TAG, "CRITICAL_CLEANUP: Still over quota after cleanup - disabling preloading")
            // Here you would disable preloading services
        }

        return result
    }

    /**
     * Records a quota violation for tracking.
     */
    private fun recordQuotaViolation() {
        val currentCount = preferencesHelper.getInt(KEY_QUOTA_VIOLATIONS, 0)
        preferencesHelper.saveInt(KEY_QUOTA_VIOLATIONS, currentCount + 1)
        BatteryLogger.w(TAG, "QUOTA_VIOLATION: Recorded violation #${currentCount + 1}")
    }

    /**
     * Generates enforcement message based on action taken.
     */
    private fun generateEnforcementMessage(action: QuotaAction, report: QuotaUsageReport): String {
        return when (action) {
            QuotaAction.NONE -> "Quota usage is normal (${String.format("%.1f", report.totalUsagePercent)}%)"
            QuotaAction.WARNING_ISSUED -> "Warning: Quota usage is high (${String.format("%.1f", report.totalUsagePercent)}%)"
            QuotaAction.CLEANUP_PERFORMED -> "Cleanup performed due to quota usage (${String.format("%.1f", report.totalUsagePercent)}%)"
            QuotaAction.EMERGENCY_CLEANUP -> "Emergency cleanup performed - quota was at ${String.format("%.1f", report.totalUsagePercent)}%"
            QuotaAction.CRITICAL_CLEANUP -> "Critical cleanup performed - quota exceeded ${String.format("%.1f", report.totalUsagePercent)}%"
            QuotaAction.PRELOADING_DISABLED -> "Preloading disabled due to critical quota usage (${String.format("%.1f", report.totalUsagePercent)}%)"
        }
    }

    /**
     * Exports quota usage data for analysis.
     */
    suspend fun exportQuotaData(): String = withContext(Dispatchers.IO) {
        val report = generateQuotaReport()
        val exportData = StringBuilder()

        exportData.appendLine("TJ_BatteryOne Quota Usage Export")
        exportData.appendLine("Generated: ${System.currentTimeMillis()}")
        exportData.appendLine()

        exportData.appendLine("Quota Limits:")
        exportData.appendLine("  Video Quota: ${report.videoQuotaMB}MB")
        exportData.appendLine("  Thumbnail Quota: ${report.thumbnailQuotaMB}MB")
        exportData.appendLine("  Total Quota: ${report.totalQuotaMB}MB")
        exportData.appendLine()

        exportData.appendLine("Current Usage:")
        exportData.appendLine("  Video Usage: ${report.videoUsageMB}MB (${String.format("%.1f", report.videoUsagePercent)}%)")
        exportData.appendLine("  Thumbnail Usage: ${report.thumbnailUsageMB}MB (${String.format("%.1f", report.thumbnailUsagePercent)}%)")
        exportData.appendLine("  Total Usage: ${report.totalUsageMB}MB (${String.format("%.1f", report.totalUsagePercent)}%)")
        exportData.appendLine()

        exportData.appendLine("Status: ${report.quotaStatus}")
        exportData.appendLine("Auto Cleanup: ${_isAutoCleanupEnabled.value}")
        exportData.appendLine("Warnings Enabled: ${_areWarningsEnabled.value}")
        exportData.appendLine("Quota Violations: ${getQuotaViolationCount()}")
        exportData.appendLine()

        exportData.appendLine("Recommended Actions:")
        report.recommendedActions.forEach { action ->
            exportData.appendLine("  - $action")
        }

        exportData.toString()
    }

    /**
     * Resets quota violation counter.
     */
    fun resetQuotaViolations() {
        preferencesHelper.saveInt(KEY_QUOTA_VIOLATIONS, 0)
        BatteryLogger.d(TAG, "QUOTA_VIOLATIONS: Violation counter reset")
    }

    /**
     * Gets time since last quota check.
     */
    fun getTimeSinceLastCheck(): Long {
        val lastCheck = preferencesHelper.getLong(KEY_LAST_QUOTA_CHECK, 0L)
        return if (lastCheck > 0) {
            System.currentTimeMillis() - lastCheck
        } else {
            -1L // Never checked
        }
    }

    /**
     * Checks if quota enforcement is needed based on time interval.
     */
    fun shouldPerformQuotaCheck(): Boolean {
        val timeSinceLastCheck = getTimeSinceLastCheck()
        val checkInterval = 30 * 60 * 1000L // 30 minutes

        return timeSinceLastCheck < 0 || timeSinceLastCheck > checkInterval
    }

    /**
     * Gets current quota settings.
     */
    fun getQuotaSettings(): Map<String, Any> {
        return mapOf(
            "video_quota_mb" to VIDEO_QUOTA_BYTES / BYTES_TO_MB,
            "thumbnail_quota_mb" to THUMBNAIL_QUOTA_BYTES / BYTES_TO_MB,
            "total_quota_mb" to TOTAL_QUOTA_BYTES / BYTES_TO_MB,
            "auto_cleanup_enabled" to _isAutoCleanupEnabled.value,
            "warnings_enabled" to _areWarningsEnabled.value,
            "warning_threshold" to WARNING_THRESHOLD,
            "emergency_threshold" to EMERGENCY_THRESHOLD,
            "critical_threshold" to CRITICAL_THRESHOLD,
            "current_status" to _quotaStatus.value.name,
            "violation_count" to getQuotaViolationCount(),
            "time_since_last_check_ms" to getTimeSinceLastCheck()
        )
    }
}
