package com.tqhit.battery.one.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.StatFs
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Comprehensive memory and storage analyzer for TJ_BatteryOne app.
 * Monitors RAM usage, storage consumption, and performance metrics for video/thumbnail preloading.
 * 
 * Performance Targets:
 * - Video cache: <50MB total storage
 * - Thumbnail cache: <10MB total storage
 * - Memory usage: <100MB RAM for preloading operations
 * - Load times: <500ms for animations, <100ms for thumbnails
 */
@Singleton
class MemoryAnalyzer @Inject constructor(
    @ApplicationContext private val context: Context,
    private val animationFileManager: AnimationFileManager,
    private val thumbnailFileManager: ThumbnailFileManager,
    private val animationPreloadingRepository: AnimationPreloadingRepository,
    private val thumbnailPreloadingRepository: ThumbnailPreloadingRepository
) {
    companion object {
        private const val TAG = "MemoryAnalyzer"
        
        // Performance thresholds
        private const val MAX_VIDEO_CACHE_SIZE_MB = 50L
        private const val MAX_THUMBNAIL_CACHE_SIZE_MB = 10L
        private const val MAX_PRELOAD_MEMORY_MB = 100L
        private const val TARGET_ANIMATION_LOAD_TIME_MS = 500L
        private const val TARGET_THUMBNAIL_LOAD_TIME_MS = 100L
        
        // Memory monitoring constants
        private const val BYTES_TO_MB = 1024 * 1024L
        private const val BYTES_TO_KB = 1024L
    }
    
    /**
     * Comprehensive memory analysis result.
     */
    data class MemoryAnalysisResult(
        val appMemoryUsageMB: Long,
        val availableMemoryMB: Long,
        val totalMemoryMB: Long,
        val videoCacheSizeMB: Long,
        val thumbnailCacheSizeMB: Long,
        val totalStorageUsageMB: Long,
        val availableStorageMB: Long,
        val preloadedVideoCount: Int,
        val preloadedThumbnailCount: Int,
        val memoryPressureLevel: MemoryPressureLevel,
        val performanceIssues: List<PerformanceIssue>,
        val recommendations: List<String>
    )
    
    /**
     * Memory pressure levels for adaptive behavior.
     */
    enum class MemoryPressureLevel {
        LOW,      // <50% memory usage
        MODERATE, // 50-75% memory usage
        HIGH,     // 75-90% memory usage
        CRITICAL  // >90% memory usage
    }
    
    /**
     * Performance issue types.
     */
    data class PerformanceIssue(
        val type: IssueType,
        val description: String,
        val severity: Severity,
        val currentValue: String,
        val targetValue: String
    )
    
    enum class IssueType {
        MEMORY_USAGE,
        STORAGE_USAGE,
        CACHE_SIZE,
        LOAD_TIME,
        FILE_COUNT
    }
    
    enum class Severity {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }
    
    /**
     * Performs comprehensive memory and storage analysis.
     */
    suspend fun analyzeMemoryUsage(): MemoryAnalysisResult = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS: Starting comprehensive memory analysis")
        
        val startTime = System.currentTimeMillis()
        
        try {
            // Collect memory metrics
            val memoryInfo = getMemoryInfo()
            val storageInfo = getStorageInfo()
            val cacheInfo = getCacheInfo()
            val preloadInfo = getPreloadInfo()
            
            // Analyze performance issues
            val issues = analyzePerformanceIssues(memoryInfo, storageInfo, cacheInfo, preloadInfo)
            val recommendations = generateRecommendations(issues)
            
            val result = MemoryAnalysisResult(
                appMemoryUsageMB = memoryInfo.appMemoryUsageMB,
                availableMemoryMB = memoryInfo.availableMemoryMB,
                totalMemoryMB = memoryInfo.totalMemoryMB,
                videoCacheSizeMB = cacheInfo.videoCacheSizeMB,
                thumbnailCacheSizeMB = cacheInfo.thumbnailCacheSizeMB,
                totalStorageUsageMB = storageInfo.totalUsageMB,
                availableStorageMB = storageInfo.availableMB,
                preloadedVideoCount = preloadInfo.videoCount,
                preloadedThumbnailCount = preloadInfo.thumbnailCount,
                memoryPressureLevel = calculateMemoryPressure(memoryInfo),
                performanceIssues = issues,
                recommendations = recommendations
            )
            
            val analysisTime = System.currentTimeMillis() - startTime
            BatteryLogger.d(TAG, "MEMORY_ANALYSIS: Analysis completed in ${analysisTime}ms")
            
            logAnalysisResults(result)
            result
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "MEMORY_ANALYSIS: Error during analysis", e)
            throw e
        }
    }
    
    /**
     * Gets current memory information.
     */
    private fun getMemoryInfo(): MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        // Get app-specific memory usage
        val debugMemoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(debugMemoryInfo)
        
        return MemoryInfo(
            appMemoryUsageMB = debugMemoryInfo.totalPss / BYTES_TO_KB, // PSS is in KB
            availableMemoryMB = memoryInfo.availMem / BYTES_TO_MB,
            totalMemoryMB = memoryInfo.totalMem / BYTES_TO_MB,
            lowMemoryThreshold = memoryInfo.threshold / BYTES_TO_MB,
            isLowMemory = memoryInfo.lowMemory
        )
    }
    
    private data class MemoryInfo(
        val appMemoryUsageMB: Long,
        val availableMemoryMB: Long,
        val totalMemoryMB: Long,
        val lowMemoryThreshold: Long,
        val isLowMemory: Boolean
    )
    
    /**
     * Gets current storage information.
     */
    private fun getStorageInfo(): StorageInfo {
        val internalDir = context.filesDir
        val statFs = StatFs(internalDir.path)
        
        val blockSize = statFs.blockSizeLong
        val totalBlocks = statFs.blockCountLong
        val availableBlocks = statFs.availableBlocksLong
        
        val totalStorageMB = (totalBlocks * blockSize) / BYTES_TO_MB
        val availableStorageMB = (availableBlocks * blockSize) / BYTES_TO_MB
        val usedStorageMB = totalStorageMB - availableStorageMB
        
        return StorageInfo(
            totalMB = totalStorageMB,
            availableMB = availableStorageMB,
            totalUsageMB = usedStorageMB
        )
    }
    
    private data class StorageInfo(
        val totalMB: Long,
        val availableMB: Long,
        val totalUsageMB: Long
    )
    
    /**
     * Gets cache size information.
     */
    private suspend fun getCacheInfo(): CacheInfo = withContext(Dispatchers.IO) {
        val videoCacheSize = animationFileManager.getTotalPreloadedSize()
        val thumbnailCacheSize = thumbnailFileManager.getTotalThumbnailSize()

        return@withContext CacheInfo(
            videoCacheSizeMB = videoCacheSize / BYTES_TO_MB,
            thumbnailCacheSizeMB = thumbnailCacheSize / BYTES_TO_MB
        )
    }
    
    private data class CacheInfo(
        val videoCacheSizeMB: Long,
        val thumbnailCacheSizeMB: Long
    )
    
    /**
     * Gets preload information.
     */
    private suspend fun getPreloadInfo(): PreloadInfo = withContext(Dispatchers.IO) {
        val videoStats = animationPreloadingRepository.getPreloadingStats()
        val thumbnailStats = thumbnailPreloadingRepository.getThumbnailPreloadingStats()

        return@withContext PreloadInfo(
            videoCount = videoStats.preloadedFileCount,
            thumbnailCount = thumbnailStats.preloadedCount
        )
    }
    
    private data class PreloadInfo(
        val videoCount: Int,
        val thumbnailCount: Int
    )
    
    /**
     * Calculates directory size recursively.
     */
    private fun calculateDirectorySize(directory: File): Long {
        if (!directory.exists() || !directory.isDirectory) return 0L
        
        var size = 0L
        directory.listFiles()?.forEach { file ->
            size += if (file.isDirectory) {
                calculateDirectorySize(file)
            } else {
                file.length()
            }
        }
        return size
    }
    
    /**
     * Calculates memory pressure level.
     */
    private fun calculateMemoryPressure(memoryInfo: MemoryInfo): MemoryPressureLevel {
        val usedMemoryMB = memoryInfo.totalMemoryMB - memoryInfo.availableMemoryMB
        val memoryUsagePercent = (usedMemoryMB.toDouble() / memoryInfo.totalMemoryMB.toDouble()) * 100

        return when {
            memoryUsagePercent > 90 -> MemoryPressureLevel.CRITICAL
            memoryUsagePercent > 75 -> MemoryPressureLevel.HIGH
            memoryUsagePercent > 50 -> MemoryPressureLevel.MODERATE
            else -> MemoryPressureLevel.LOW
        }
    }

    /**
     * Analyzes performance issues based on collected metrics.
     */
    private fun analyzePerformanceIssues(
        memoryInfo: MemoryInfo,
        storageInfo: StorageInfo,
        cacheInfo: CacheInfo,
        preloadInfo: PreloadInfo
    ): List<PerformanceIssue> {
        val issues = mutableListOf<PerformanceIssue>()

        // Check memory usage
        if (memoryInfo.appMemoryUsageMB > MAX_PRELOAD_MEMORY_MB) {
            issues.add(
                PerformanceIssue(
                    type = IssueType.MEMORY_USAGE,
                    description = "App memory usage exceeds recommended threshold",
                    severity = if (memoryInfo.appMemoryUsageMB > MAX_PRELOAD_MEMORY_MB * 1.5) Severity.HIGH else Severity.MEDIUM,
                    currentValue = "${memoryInfo.appMemoryUsageMB}MB",
                    targetValue = "<${MAX_PRELOAD_MEMORY_MB}MB"
                )
            )
        }

        // Check video cache size
        if (cacheInfo.videoCacheSizeMB > MAX_VIDEO_CACHE_SIZE_MB) {
            issues.add(
                PerformanceIssue(
                    type = IssueType.CACHE_SIZE,
                    description = "Video cache size exceeds recommended limit",
                    severity = if (cacheInfo.videoCacheSizeMB > MAX_VIDEO_CACHE_SIZE_MB * 1.5) Severity.HIGH else Severity.MEDIUM,
                    currentValue = "${cacheInfo.videoCacheSizeMB}MB",
                    targetValue = "<${MAX_VIDEO_CACHE_SIZE_MB}MB"
                )
            )
        }

        // Check thumbnail cache size
        if (cacheInfo.thumbnailCacheSizeMB > MAX_THUMBNAIL_CACHE_SIZE_MB) {
            issues.add(
                PerformanceIssue(
                    type = IssueType.CACHE_SIZE,
                    description = "Thumbnail cache size exceeds recommended limit",
                    severity = if (cacheInfo.thumbnailCacheSizeMB > MAX_THUMBNAIL_CACHE_SIZE_MB * 1.5) Severity.HIGH else Severity.MEDIUM,
                    currentValue = "${cacheInfo.thumbnailCacheSizeMB}MB",
                    targetValue = "<${MAX_THUMBNAIL_CACHE_SIZE_MB}MB"
                )
            )
        }

        // Check storage availability
        if (storageInfo.availableMB < 100) { // Less than 100MB available
            issues.add(
                PerformanceIssue(
                    type = IssueType.STORAGE_USAGE,
                    description = "Low storage space available",
                    severity = if (storageInfo.availableMB < 50) Severity.CRITICAL else Severity.HIGH,
                    currentValue = "${storageInfo.availableMB}MB available",
                    targetValue = ">100MB available"
                )
            )
        }

        // Check file count
        val totalFiles = preloadInfo.videoCount + preloadInfo.thumbnailCount
        if (totalFiles > 50) { // Too many cached files
            issues.add(
                PerformanceIssue(
                    type = IssueType.FILE_COUNT,
                    description = "High number of cached files may impact performance",
                    severity = if (totalFiles > 100) Severity.MEDIUM else Severity.LOW,
                    currentValue = "$totalFiles files",
                    targetValue = "<50 files"
                )
            )
        }

        return issues
    }

    /**
     * Generates recommendations based on identified issues.
     */
    private fun generateRecommendations(issues: List<PerformanceIssue>): List<String> {
        val recommendations = mutableListOf<String>()

        issues.forEach { issue ->
            when (issue.type) {
                IssueType.MEMORY_USAGE -> {
                    recommendations.add("Implement memory-efficient Glide configuration with smaller cache sizes")
                    recommendations.add("Add memory pressure monitoring to reduce preloading during high memory usage")
                    recommendations.add("Implement lazy loading for thumbnails to reduce memory footprint")
                }
                IssueType.CACHE_SIZE -> {
                    recommendations.add("Implement cache size limits with LRU eviction policy")
                    recommendations.add("Add periodic cache cleanup based on file age and usage frequency")
                    recommendations.add("Compress cached files to reduce storage usage")
                }
                IssueType.STORAGE_USAGE -> {
                    recommendations.add("Implement aggressive cache cleanup when storage is low")
                    recommendations.add("Reduce number of preloaded files during low storage conditions")
                    recommendations.add("Add storage monitoring to prevent app crashes due to insufficient space")
                }
                IssueType.FILE_COUNT -> {
                    recommendations.add("Implement file count limits with oldest-first cleanup strategy")
                    recommendations.add("Prioritize most frequently used animations for caching")
                    recommendations.add("Add file usage tracking to optimize cache retention")
                }
                IssueType.LOAD_TIME -> {
                    recommendations.add("Optimize file compression and loading algorithms")
                    recommendations.add("Implement progressive loading for large files")
                    recommendations.add("Add background preloading during idle periods")
                }
            }
        }

        // Remove duplicates and return
        return recommendations.distinct()
    }

    /**
     * Logs comprehensive analysis results.
     */
    private fun logAnalysisResults(result: MemoryAnalysisResult) {
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: ===== Memory Analysis Report =====")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: App Memory Usage: ${result.appMemoryUsageMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Available Memory: ${result.availableMemoryMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Total Memory: ${result.totalMemoryMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Video Cache Size: ${result.videoCacheSizeMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Thumbnail Cache Size: ${result.thumbnailCacheSizeMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Total Storage Usage: ${result.totalStorageUsageMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Available Storage: ${result.availableStorageMB}MB")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Preloaded Videos: ${result.preloadedVideoCount}")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Preloaded Thumbnails: ${result.preloadedThumbnailCount}")
        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Memory Pressure: ${result.memoryPressureLevel}")

        if (result.performanceIssues.isNotEmpty()) {
            BatteryLogger.w(TAG, "MEMORY_ANALYSIS_RESULTS: Performance Issues Found: ${result.performanceIssues.size}")
            result.performanceIssues.forEach { issue ->
                BatteryLogger.w(TAG, "MEMORY_ANALYSIS_RESULTS: ${issue.severity} - ${issue.description} (${issue.currentValue} vs ${issue.targetValue})")
            }
        } else {
            BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: No performance issues detected")
        }

        if (result.recommendations.isNotEmpty()) {
            BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: Recommendations:")
            result.recommendations.forEach { recommendation ->
                BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: - $recommendation")
            }
        }

        BatteryLogger.d(TAG, "MEMORY_ANALYSIS_RESULTS: ===== End Analysis Report =====")
    }

    /**
     * Monitors memory usage during specific operations.
     */
    suspend fun monitorOperationMemory(
        operationName: String,
        operation: suspend () -> Unit
    ): OperationMemoryResult = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        val startMemory = getMemoryInfo()

        BatteryLogger.d(TAG, "MEMORY_MONITOR: Starting operation '$operationName' - Memory: ${startMemory.appMemoryUsageMB}MB")

        try {
            operation()

            val endTime = System.currentTimeMillis()
            val endMemory = getMemoryInfo()
            val duration = endTime - startTime
            val memoryDelta = endMemory.appMemoryUsageMB - startMemory.appMemoryUsageMB

            val result = OperationMemoryResult(
                operationName = operationName,
                durationMs = duration,
                memoryDeltaMB = memoryDelta,
                startMemoryMB = startMemory.appMemoryUsageMB,
                endMemoryMB = endMemory.appMemoryUsageMB,
                success = true
            )

            BatteryLogger.d(TAG, "MEMORY_MONITOR: Operation '$operationName' completed - Duration: ${duration}ms, Memory Delta: ${memoryDelta}MB")

            if (memoryDelta > 10) { // More than 10MB increase
                BatteryLogger.w(TAG, "MEMORY_MONITOR: High memory usage detected for operation '$operationName': ${memoryDelta}MB")
            }

            result

        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            val endMemory = getMemoryInfo()

            BatteryLogger.e(TAG, "MEMORY_MONITOR: Operation '$operationName' failed", e)

            OperationMemoryResult(
                operationName = operationName,
                durationMs = endTime - startTime,
                memoryDeltaMB = endMemory.appMemoryUsageMB - startMemory.appMemoryUsageMB,
                startMemoryMB = startMemory.appMemoryUsageMB,
                endMemoryMB = endMemory.appMemoryUsageMB,
                success = false,
                error = e.message
            )
        }
    }

    /**
     * Result of memory monitoring for a specific operation.
     */
    data class OperationMemoryResult(
        val operationName: String,
        val durationMs: Long,
        val memoryDeltaMB: Long,
        val startMemoryMB: Long,
        val endMemoryMB: Long,
        val success: Boolean,
        val error: String? = null
    )
}
