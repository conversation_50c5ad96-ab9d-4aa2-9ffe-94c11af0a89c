package com.tqhit.battery.one.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import com.tqhit.battery.one.manager.storage.OptimizedStorageManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Comprehensive performance profiler for TJ_BatteryOne app.
 * Monitors memory usage, load times, and provides ADB-compatible logging for performance validation.
 * 
 * Performance Benchmarks:
 * - Animation load time: <500ms
 * - Thumbnail load time: <100ms
 * - Memory usage: <100MB for preloading operations
 * - Cold start time: <3000ms
 * - Fragment switching: <500ms
 */
@Singleton
class PerformanceProfiler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val memoryAnalyzer: MemoryAnalyzer,
    private val storageManager: OptimizedStorageManager
) {
    companion object {
        private const val TAG = "PerformanceProfiler"
        
        // Performance benchmarks (in milliseconds)
        private const val ANIMATION_LOAD_BENCHMARK_MS = 500L
        private const val THUMBNAIL_LOAD_BENCHMARK_MS = 100L
        private const val COLD_START_BENCHMARK_MS = 3000L
        private const val FRAGMENT_SWITCH_BENCHMARK_MS = 500L
        private const val DATA_FLOW_BENCHMARK_MS = 100L
        
        // Memory benchmarks (in MB)
        private const val PRELOAD_MEMORY_BENCHMARK_MB = 100L
        
        // Monitoring intervals
        private const val MEMORY_MONITOR_INTERVAL_MS = 10_000L // 10 seconds
        private const val PERFORMANCE_LOG_INTERVAL_MS = 30_000L // 30 seconds
        
        // ADB logcat tags for monitoring
        const val LOGCAT_TAG_STARTUP_TIMING = "STARTUP_TIMING"
        const val LOGCAT_TAG_FRAGMENT_SWITCH = "FRAGMENT_SWITCH"
        const val LOGCAT_TAG_DATA_FLOW_LATENCY = "DATA_FLOW_LATENCY"
        const val LOGCAT_TAG_MEMORY_USAGE = "MEMORY_USAGE"
        const val LOGCAT_TAG_PERFORMANCE_BENCHMARK = "PERFORMANCE_BENCHMARK"
    }
    
    // Performance tracking
    private val operationTimes = ConcurrentHashMap<String, MutableList<Long>>()
    private val memorySnapshots = ConcurrentHashMap<Long, MemorySnapshot>()
    private val performanceViolations = mutableListOf<PerformanceViolation>()
    
    // Monitoring state
    private val _isMonitoring = MutableStateFlow(false)
    val isMonitoring: StateFlow<Boolean> = _isMonitoring
    
    private var monitoringJob: Job? = null
    private val monitoringScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // Performance metrics
    private val totalOperations = AtomicLong(0)
    private val violationCount = AtomicLong(0)
    
    /**
     * Performance violation data class.
     */
    data class PerformanceViolation(
        val operationType: String,
        val actualTimeMs: Long,
        val benchmarkMs: Long,
        val timestamp: Long,
        val severity: ViolationSeverity,
        val context: String
    )
    
    enum class ViolationSeverity {
        MINOR,    // 10-25% over benchmark
        MODERATE, // 25-50% over benchmark
        MAJOR,    // 50-100% over benchmark
        CRITICAL  // >100% over benchmark
    }
    
    /**
     * Memory snapshot for tracking.
     */
    data class MemorySnapshot(
        val timestamp: Long,
        val appMemoryMB: Long,
        val availableMemoryMB: Long,
        val totalMemoryMB: Long,
        val videoCacheMB: Long,
        val thumbnailCacheMB: Long,
        val operationContext: String
    )
    
    /**
     * Performance report summary.
     */
    data class PerformanceReport(
        val totalOperations: Long,
        val violationCount: Long,
        val violationRate: Double,
        val averageMemoryUsageMB: Long,
        val peakMemoryUsageMB: Long,
        val operationAverages: Map<String, Long>,
        val recentViolations: List<PerformanceViolation>,
        val recommendations: List<String>
    )
    
    /**
     * Starts comprehensive performance monitoring.
     */
    fun startMonitoring() {
        if (_isMonitoring.value) {
            BatteryLogger.w(TAG, "PERFORMANCE_MONITOR: Monitoring already active")
            return
        }
        
        BatteryLogger.d(TAG, "PERFORMANCE_MONITOR: Starting performance monitoring")
        _isMonitoring.value = true
        
        monitoringJob = monitoringScope.launch {
            while (_isMonitoring.value) {
                try {
                    performPeriodicMonitoring()
                    delay(MEMORY_MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    BatteryLogger.e(TAG, "PERFORMANCE_MONITOR: Error during monitoring", e)
                }
            }
        }
        
        // Log monitoring start for ADB
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "MONITORING_STARTED: Performance monitoring activated")
    }
    
    /**
     * Stops performance monitoring.
     */
    fun stopMonitoring() {
        BatteryLogger.d(TAG, "PERFORMANCE_MONITOR: Stopping performance monitoring")
        _isMonitoring.value = false
        monitoringJob?.cancel()
        
        // Log final report for ADB
        monitoringScope.launch {
            val report = generatePerformanceReport()
            logPerformanceReportForADB(report)
        }
    }
    
    /**
     * Measures operation performance with automatic violation detection.
     */
    suspend fun <T> measureOperation(
        operationType: String,
        benchmarkMs: Long,
        context: String = "",
        operation: suspend () -> T
    ): T {
        val startTime = SystemClock.elapsedRealtime()
        val startMemory = getCurrentMemoryUsage()
        
        BatteryLogger.d(TAG, "OPERATION_START: $operationType - Context: $context")
        
        try {
            val result = operation()
            
            val endTime = SystemClock.elapsedRealtime()
            val duration = endTime - startTime
            val endMemory = getCurrentMemoryUsage()
            
            // Record operation time
            recordOperationTime(operationType, duration)
            
            // Check for performance violations
            checkPerformanceViolation(operationType, duration, benchmarkMs, context)
            
            // Log for ADB monitoring
            logOperationForADB(operationType, duration, benchmarkMs, startMemory, endMemory, context)
            
            BatteryLogger.d(TAG, "OPERATION_COMPLETE: $operationType - Duration: ${duration}ms, Benchmark: ${benchmarkMs}ms")
            
            return result
            
        } catch (e: Exception) {
            val endTime = SystemClock.elapsedRealtime()
            val duration = endTime - startTime
            
            BatteryLogger.e(TAG, "OPERATION_FAILED: $operationType - Duration: ${duration}ms, Error: ${e.message}")
            
            // Log failure for ADB
            BatteryLogger.e(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "OPERATION_FAILED: $operationType duration=${duration}ms error=${e.message}")
            
            throw e
        }
    }
    
    /**
     * Measures animation load performance.
     */
    suspend fun <T> measureAnimationLoad(
        animationUrl: String,
        operation: suspend () -> T
    ): T {
        return measureOperation(
            operationType = "animation_load",
            benchmarkMs = ANIMATION_LOAD_BENCHMARK_MS,
            context = "url=$animationUrl",
            operation = operation
        )
    }
    
    /**
     * Measures thumbnail load performance.
     */
    suspend fun <T> measureThumbnailLoad(
        thumbnailUrl: String,
        operation: suspend () -> T
    ): T {
        return measureOperation(
            operationType = "thumbnail_load",
            benchmarkMs = THUMBNAIL_LOAD_BENCHMARK_MS,
            context = "url=$thumbnailUrl",
            operation = operation
        )
    }
    
    /**
     * Measures fragment switching performance.
     */
    suspend fun <T> measureFragmentSwitch(
        fromFragment: String,
        toFragment: String,
        operation: suspend () -> T
    ): T {
        return measureOperation(
            operationType = "fragment_switch",
            benchmarkMs = FRAGMENT_SWITCH_BENCHMARK_MS,
            context = "from=$fromFragment to=$toFragment",
            operation = operation
        )
    }
    
    /**
     * Measures data flow latency.
     */
    suspend fun <T> measureDataFlow(
        dataType: String,
        operation: suspend () -> T
    ): T {
        return measureOperation(
            operationType = "data_flow",
            benchmarkMs = DATA_FLOW_BENCHMARK_MS,
            context = "type=$dataType",
            operation = operation
        )
    }
    
    /**
     * Records cold start timing for ADB monitoring.
     */
    fun recordColdStart(startTime: Long) {
        val duration = SystemClock.elapsedRealtime() - startTime
        
        BatteryLogger.d(LOGCAT_TAG_STARTUP_TIMING, "COLD_START_COMPLETE: duration=${duration}ms benchmark=${COLD_START_BENCHMARK_MS}ms")
        
        if (duration > COLD_START_BENCHMARK_MS) {
            val violation = PerformanceViolation(
                operationType = "cold_start",
                actualTimeMs = duration,
                benchmarkMs = COLD_START_BENCHMARK_MS,
                timestamp = System.currentTimeMillis(),
                severity = calculateViolationSeverity(duration, COLD_START_BENCHMARK_MS),
                context = "app_startup"
            )
            
            performanceViolations.add(violation)
            violationCount.incrementAndGet()
            
            BatteryLogger.w(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "COLD_START_VIOLATION: duration=${duration}ms benchmark=${COLD_START_BENCHMARK_MS}ms severity=${violation.severity}")
        }
    }
    
    /**
     * Generates comprehensive performance report.
     */
    suspend fun generatePerformanceReport(): PerformanceReport = withContext(Dispatchers.IO) {
        val memoryAnalysis = memoryAnalyzer.analyzeMemoryUsage()
        val storageAnalysis = storageManager.analyzeStorage()
        
        val operationAverages = operationTimes.mapValues { (_, times) ->
            if (times.isNotEmpty()) times.average().toLong() else 0L
        }
        
        val averageMemory = if (memorySnapshots.isNotEmpty()) {
            memorySnapshots.values.map { it.appMemoryMB }.average().toLong()
        } else 0L
        
        val peakMemory = memorySnapshots.values.maxOfOrNull { it.appMemoryMB } ?: 0L
        
        val recentViolations = performanceViolations.takeLast(10)
        
        val recommendations = generatePerformanceRecommendations(
            memoryAnalysis,
            storageAnalysis,
            operationAverages,
            recentViolations
        )
        
        PerformanceReport(
            totalOperations = totalOperations.get(),
            violationCount = violationCount.get(),
            violationRate = if (totalOperations.get() > 0) {
                (violationCount.get().toDouble() / totalOperations.get().toDouble()) * 100
            } else 0.0,
            averageMemoryUsageMB = averageMemory,
            peakMemoryUsageMB = peakMemory,
            operationAverages = operationAverages,
            recentViolations = recentViolations,
            recommendations = recommendations
        )
    }
    
    /**
     * Performs periodic monitoring tasks.
     */
    private suspend fun performPeriodicMonitoring() {
        try {
            // Take memory snapshot
            val memoryAnalysis = memoryAnalyzer.analyzeMemoryUsage()
            val storageAnalysis = storageManager.analyzeStorage()
            
            val snapshot = MemorySnapshot(
                timestamp = System.currentTimeMillis(),
                appMemoryMB = memoryAnalysis.appMemoryUsageMB,
                availableMemoryMB = memoryAnalysis.availableMemoryMB,
                totalMemoryMB = memoryAnalysis.totalMemoryMB,
                videoCacheMB = storageAnalysis.videoCacheSizeMB,
                thumbnailCacheMB = storageAnalysis.thumbnailCacheSizeMB,
                operationContext = "periodic_monitoring"
            )
            
            memorySnapshots[snapshot.timestamp] = snapshot
            
            // Log for ADB monitoring
            BatteryLogger.d(LOGCAT_TAG_MEMORY_USAGE, "MEMORY_SNAPSHOT: app=${snapshot.appMemoryMB}MB available=${snapshot.availableMemoryMB}MB video_cache=${snapshot.videoCacheMB}MB thumbnail_cache=${snapshot.thumbnailCacheMB}MB")
            
            // Check for memory violations
            if (snapshot.appMemoryMB > PRELOAD_MEMORY_BENCHMARK_MB) {
                BatteryLogger.w(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "MEMORY_VIOLATION: app_memory=${snapshot.appMemoryMB}MB benchmark=${PRELOAD_MEMORY_BENCHMARK_MB}MB")
            }
            
            // Cleanup old snapshots (keep last 100)
            if (memorySnapshots.size > 100) {
                val oldestKeys = memorySnapshots.keys.sorted().take(memorySnapshots.size - 100)
                oldestKeys.forEach { memorySnapshots.remove(it) }
            }
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "PERIODIC_MONITORING: Error during periodic monitoring", e)
        }
    }
    
    /**
     * Records operation time for statistics.
     */
    private fun recordOperationTime(operationType: String, duration: Long) {
        operationTimes.computeIfAbsent(operationType) { mutableListOf() }.add(duration)
        totalOperations.incrementAndGet()
        
        // Keep only last 50 measurements per operation type
        operationTimes[operationType]?.let { times ->
            if (times.size > 50) {
                times.removeAt(0)
            }
        }
    }
    
    /**
     * Checks for performance violations.
     */
    private fun checkPerformanceViolation(
        operationType: String,
        actualTime: Long,
        benchmarkTime: Long,
        context: String
    ) {
        if (actualTime > benchmarkTime) {
            val violation = PerformanceViolation(
                operationType = operationType,
                actualTimeMs = actualTime,
                benchmarkMs = benchmarkTime,
                timestamp = System.currentTimeMillis(),
                severity = calculateViolationSeverity(actualTime, benchmarkTime),
                context = context
            )
            
            performanceViolations.add(violation)
            violationCount.incrementAndGet()
            
            BatteryLogger.w(TAG, "PERFORMANCE_VIOLATION: $operationType - Actual: ${actualTime}ms, Benchmark: ${benchmarkTime}ms, Severity: ${violation.severity}")
        }
    }
    
    /**
     * Calculates violation severity based on how much the benchmark was exceeded.
     */
    private fun calculateViolationSeverity(actualTime: Long, benchmarkTime: Long): ViolationSeverity {
        val ratio = actualTime.toDouble() / benchmarkTime.toDouble()
        return when {
            ratio <= 1.25 -> ViolationSeverity.MINOR
            ratio <= 1.5 -> ViolationSeverity.MODERATE
            ratio <= 2.0 -> ViolationSeverity.MAJOR
            else -> ViolationSeverity.CRITICAL
        }
    }
    
    /**
     * Gets current memory usage.
     */
    private fun getCurrentMemoryUsage(): Long {
        val debugMemoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(debugMemoryInfo)
        return debugMemoryInfo.totalPss / 1024L // Convert KB to MB
    }

    /**
     * Logs operation performance for ADB monitoring.
     */
    private fun logOperationForADB(
        operationType: String,
        duration: Long,
        benchmark: Long,
        startMemory: Long,
        endMemory: Long,
        context: String
    ) {
        val memoryDelta = endMemory - startMemory
        val status = if (duration <= benchmark) "PASS" else "FAIL"

        when (operationType) {
            "fragment_switch" -> {
                BatteryLogger.d(LOGCAT_TAG_FRAGMENT_SWITCH, "FRAGMENT_SWITCH_TIMING: duration=${duration}ms benchmark=${benchmark}ms status=$status memory_delta=${memoryDelta}MB context=$context")
            }
            "data_flow" -> {
                BatteryLogger.d(LOGCAT_TAG_DATA_FLOW_LATENCY, "DATA_FLOW_TIMING: duration=${duration}ms benchmark=${benchmark}ms status=$status context=$context")
            }
            else -> {
                BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "OPERATION_TIMING: type=$operationType duration=${duration}ms benchmark=${benchmark}ms status=$status memory_delta=${memoryDelta}MB context=$context")
            }
        }
    }

    /**
     * Logs performance report for ADB monitoring.
     */
    private fun logPerformanceReportForADB(report: PerformanceReport) {
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT_START: ===== Performance Report =====")
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: total_operations=${report.totalOperations}")
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: violation_count=${report.violationCount}")
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: violation_rate=${String.format("%.2f", report.violationRate)}%")
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: average_memory=${report.averageMemoryUsageMB}MB")
        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: peak_memory=${report.peakMemoryUsageMB}MB")

        report.operationAverages.forEach { (operation, average) ->
            BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: ${operation}_average=${average}ms")
        }

        if (report.recentViolations.isNotEmpty()) {
            BatteryLogger.w(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT: recent_violations=${report.recentViolations.size}")
            report.recentViolations.forEach { violation ->
                BatteryLogger.w(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_VIOLATION: ${violation.operationType} actual=${violation.actualTimeMs}ms benchmark=${violation.benchmarkMs}ms severity=${violation.severity}")
            }
        }

        BatteryLogger.d(LOGCAT_TAG_PERFORMANCE_BENCHMARK, "PERFORMANCE_REPORT_END: ===== End Performance Report =====")
    }

    /**
     * Generates performance recommendations based on analysis.
     */
    private fun generatePerformanceRecommendations(
        memoryAnalysis: MemoryAnalyzer.MemoryAnalysisResult,
        storageAnalysis: OptimizedStorageManager.StorageAnalysis,
        operationAverages: Map<String, Long>,
        recentViolations: List<PerformanceViolation>
    ): List<String> {
        val recommendations = mutableListOf<String>()

        // Memory-based recommendations
        if (memoryAnalysis.appMemoryUsageMB > PRELOAD_MEMORY_BENCHMARK_MB) {
            recommendations.add("Reduce memory usage: Current ${memoryAnalysis.appMemoryUsageMB}MB exceeds ${PRELOAD_MEMORY_BENCHMARK_MB}MB benchmark")
            recommendations.add("Consider implementing more aggressive memory cleanup strategies")
        }

        if (memoryAnalysis.memoryPressureLevel >= MemoryAnalyzer.MemoryPressureLevel.HIGH) {
            recommendations.add("High memory pressure detected - implement memory-aware loading strategies")
            recommendations.add("Reduce preloading batch sizes during high memory conditions")
        }

        // Storage-based recommendations
        if (storageAnalysis.needsCleanup) {
            recommendations.add("Storage cleanup needed: Video cache ${storageAnalysis.videoCacheSizeMB}MB, Thumbnail cache ${storageAnalysis.thumbnailCacheSizeMB}MB")
        }

        if (storageAnalysis.isStorageLow) {
            recommendations.add("Critical: Low storage space (${storageAnalysis.availableSpaceMB}MB available)")
        }

        // Operation-based recommendations
        operationAverages.forEach { (operation, average) ->
            val benchmark = when (operation) {
                "animation_load" -> ANIMATION_LOAD_BENCHMARK_MS
                "thumbnail_load" -> THUMBNAIL_LOAD_BENCHMARK_MS
                "fragment_switch" -> FRAGMENT_SWITCH_BENCHMARK_MS
                "data_flow" -> DATA_FLOW_BENCHMARK_MS
                else -> 0L
            }

            if (benchmark > 0 && average > benchmark) {
                recommendations.add("Optimize $operation performance: Average ${average}ms exceeds ${benchmark}ms benchmark")
            }
        }

        // Violation-based recommendations
        val criticalViolations = recentViolations.count { it.severity == ViolationSeverity.CRITICAL }
        if (criticalViolations > 0) {
            recommendations.add("Address $criticalViolations critical performance violations")
        }

        val majorViolations = recentViolations.count { it.severity == ViolationSeverity.MAJOR }
        if (majorViolations > 2) {
            recommendations.add("Multiple major performance violations detected - review loading strategies")
        }

        // General recommendations
        if (recommendations.isEmpty()) {
            recommendations.add("Performance is within acceptable benchmarks")
        } else {
            recommendations.add("Consider implementing lazy loading and progressive enhancement strategies")
            recommendations.add("Monitor memory usage during peak operations")
            recommendations.add("Implement adaptive quality based on device capabilities")
        }

        return recommendations
    }

    /**
     * Exports performance data for external analysis.
     */
    suspend fun exportPerformanceData(): String = withContext(Dispatchers.IO) {
        val report = generatePerformanceReport()
        val exportData = StringBuilder()

        exportData.appendLine("TJ_BatteryOne Performance Export")
        exportData.appendLine("Generated: ${System.currentTimeMillis()}")
        exportData.appendLine("Total Operations: ${report.totalOperations}")
        exportData.appendLine("Violation Count: ${report.violationCount}")
        exportData.appendLine("Violation Rate: ${String.format("%.2f", report.violationRate)}%")
        exportData.appendLine("Average Memory: ${report.averageMemoryUsageMB}MB")
        exportData.appendLine("Peak Memory: ${report.peakMemoryUsageMB}MB")
        exportData.appendLine()

        exportData.appendLine("Operation Averages:")
        report.operationAverages.forEach { (operation, average) ->
            exportData.appendLine("  $operation: ${average}ms")
        }
        exportData.appendLine()

        exportData.appendLine("Recent Violations:")
        report.recentViolations.forEach { violation ->
            exportData.appendLine("  ${violation.operationType}: ${violation.actualTimeMs}ms (benchmark: ${violation.benchmarkMs}ms) - ${violation.severity}")
        }
        exportData.appendLine()

        exportData.appendLine("Recommendations:")
        report.recommendations.forEach { recommendation ->
            exportData.appendLine("  - $recommendation")
        }

        exportData.toString()
    }

    /**
     * Clears all performance data.
     */
    fun clearPerformanceData() {
        operationTimes.clear()
        memorySnapshots.clear()
        performanceViolations.clear()
        totalOperations.set(0)
        violationCount.set(0)

        BatteryLogger.d(TAG, "PERFORMANCE_DATA_CLEARED: All performance data cleared")
    }

    /**
     * Gets current performance statistics.
     */
    fun getCurrentStats(): Map<String, Any> {
        return mapOf(
            "total_operations" to totalOperations.get(),
            "violation_count" to violationCount.get(),
            "violation_rate" to if (totalOperations.get() > 0) {
                (violationCount.get().toDouble() / totalOperations.get().toDouble()) * 100
            } else 0.0,
            "memory_snapshots" to memorySnapshots.size,
            "operation_types" to operationTimes.keys.toList(),
            "is_monitoring" to _isMonitoring.value
        )
    }
}
