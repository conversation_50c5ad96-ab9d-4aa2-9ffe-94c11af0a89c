package com.tqhit.battery.one.service.thumbnail;

import android.content.Context;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.firebase.FirebaseInitializationMonitor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DeferredThumbnailPreloadingService_Factory implements Factory<DeferredThumbnailPreloadingService> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationDataService> animationDataServiceProvider;

  private final Provider<ThumbnailDataService> thumbnailDataServiceProvider;

  private final Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider;

  private final Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider;

  public DeferredThumbnailPreloadingService_Factory(Provider<Context> contextProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<ThumbnailDataService> thumbnailDataServiceProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider) {
    this.contextProvider = contextProvider;
    this.animationDataServiceProvider = animationDataServiceProvider;
    this.thumbnailDataServiceProvider = thumbnailDataServiceProvider;
    this.thumbnailPreloadingRepositoryProvider = thumbnailPreloadingRepositoryProvider;
    this.firebaseInitMonitorProvider = firebaseInitMonitorProvider;
  }

  @Override
  public DeferredThumbnailPreloadingService get() {
    return newInstance(contextProvider.get(), animationDataServiceProvider.get(), thumbnailDataServiceProvider.get(), thumbnailPreloadingRepositoryProvider.get(), firebaseInitMonitorProvider.get());
  }

  public static DeferredThumbnailPreloadingService_Factory create(Provider<Context> contextProvider,
      Provider<AnimationDataService> animationDataServiceProvider,
      Provider<ThumbnailDataService> thumbnailDataServiceProvider,
      Provider<ThumbnailPreloadingRepository> thumbnailPreloadingRepositoryProvider,
      Provider<FirebaseInitializationMonitor> firebaseInitMonitorProvider) {
    return new DeferredThumbnailPreloadingService_Factory(contextProvider, animationDataServiceProvider, thumbnailDataServiceProvider, thumbnailPreloadingRepositoryProvider, firebaseInitMonitorProvider);
  }

  public static DeferredThumbnailPreloadingService newInstance(Context context,
      AnimationDataService animationDataService, ThumbnailDataService thumbnailDataService,
      ThumbnailPreloadingRepository thumbnailPreloadingRepository,
      FirebaseInitializationMonitor firebaseInitMonitor) {
    return new DeferredThumbnailPreloadingService(context, animationDataService, thumbnailDataService, thumbnailPreloadingRepository, firebaseInitMonitor);
  }
}
